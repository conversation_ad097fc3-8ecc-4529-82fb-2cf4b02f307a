-- onnx2engine tool configuration
target("onnx2engine")
    set_kind("binary")
    set_languages("c++17")
    
    -- Add source files
    add_files("onnx2engine.cpp")
    
    -- Include directories
    add_includedirs("../include", {public = true})
    add_includedirs("../include/public", {public = true})
    add_includedirs("../include/private", {public = true})
    add_includedirs("../3rdparty/opencv2/include", {public = true})
    add_includedirs("../3rdparty/nlohmann-json_x64-windows/include", {public = true})
    add_includedirs("../3rdparty/fmt_x64-windows/include", {public = true})
    add_includedirs("../3rdparty/spdlog_x64-windows/include", {public = true})
    
    -- Link directories and libraries
    add_linkdirs("../build/windows/x64/release", {public = true})
    add_linkdirs("../3rdparty/opencv2", {public = true})
    
    -- Add runtime library dependencies
    if is_mode("debug") then
        add_links("AIFramework")
        add_links("opencv_world481d")
    else
        add_links("AIFramework")
        add_links("opencv_world481")
    end
    
    -- Windows specific settings
    if is_plat("windows") then
        add_cxflags("/utf-8")
        add_defines("WIN32", "_WINDOWS")
        if is_mode("debug") then
            add_cxflags("/MDd")
        else
            add_cxflags("/MD")
        end
    end
    
    -- Set output directory
    set_targetdir("../build/$(plat)/$(arch)/$(mode)")
    set_objectdir("../build/.objs/$(plat)/$(arch)/$(mode)")
