# TensorRT YOLO 推理完整指南

本文档详细介绍如何将ONNX模型转换为TensorRT Engine，以及如何使用Engine进行推理。

## 📋 目录

1. [环境要求](#环境要求)
2. [ONNX转Engine](#onnx转engine)
3. [Engine推理](#engine推理)
4. [性能优化](#性能优化)
5. [常见问题](#常见问题)

## 🔧 环境要求

### 硬件要求
- **GPU**: NVIDIA GPU (支持CUDA)
- **内存**: 建议8GB以上
- **存储**: 足够空间存储模型文件

### 软件要求
- **操作系统**: Windows 10/11 或 Linux
- **CUDA**: 11.8+ 
- **TensorRT**: 8.6.1.6
- **cuDNN**: 8.9.0.131
- **OpenCV**: 4.8.1
- **编译器**: Visual Studio 2019/2022 (Windows) 或 GCC 7.0+ (Linux)

### 项目依赖
```
F:\algo_ai\
├── 3rdparty/                    # 第三方库
│   ├── opencv2/                 # OpenCV库
│   ├── nlohmann-json_x64-windows/  # JSON库
│   └── spdlog_x64-windows/      # 日志库
├── sample/
│   ├── models/                  # 模型文件目录
│   │   ├── yolov5s.onnx        # ONNX模型
│   │   └── yolov5s.engine      # TensorRT引擎
│   ├── test_data/
│   │   ├── test/               # 测试图片
│   │   └── output/             # 输出结果
│   └── trt_test/               # 测试代码
└── tools/                      # 工具
    └── onnx2engine.exe         # 转换工具
```

## 🔄 ONNX转Engine

### 1. 准备ONNX模型

确保您有一个有效的ONNX模型文件：
```bash
# 检查模型文件
ls -la F:\algo_ai\sample\models\yolov5s.onnx
```

### 2. 使用onnx2engine工具转换

#### 方法一：使用项目内置工具
```bash
# 进入项目根目录
cd F:\algo_ai

# 构建转换工具
xmake build onnx2engine

# 基础转换（FP32精度）
.\build\windows\x64\release\onnx2engine.exe sample\models\yolov5s.onnx sample\models\yolov5s.engine

# 高级转换（FP16精度，批处理大小4，工作空间2GB）
.\build\windows\x64\release\onnx2engine.exe sample\models\yolov5s.onnx sample\models\yolov5s_fp16.engine --fp16 --batch 4 --workspace 2048

# 查看帮助信息
.\build\windows\x64\release\onnx2engine.exe --help
```

**onnx2engine工具参数说明：**
```
Usage: onnx2engine.exe <onnx_path> <engine_path> [options]

Arguments:
  onnx_path        输入ONNX模型文件路径
  engine_path      输出TensorRT引擎文件路径

Options:
  --fp16           使用FP16精度（默认：FP32）
  --batch <size>   最大批处理大小（默认：1）
  --workspace <MB> 工作空间大小，单位MB（默认：1024）
  --help, -h       显示帮助信息
```

#### 方法二：使用TensorRT命令行工具
```bash
# 使用trtexec工具
trtexec \
  --onnx=sample\models\yolov5s.onnx \
  --saveEngine=sample\models\yolov5s.engine \
  --explicitBatch \
  --workspace=1024 \
  --fp16
```

### 3. 转换参数说明

| 参数 | 说明 | 默认值 | 建议值 |
|------|------|--------|--------|
| `--onnx` | 输入ONNX文件路径 | 必需 | - |
| `--engine` | 输出Engine文件路径 | 必需 | - |
| `--batch-size` | 批处理大小 | 1 | 1-8 |
| `--workspace` | 工作空间大小(MB) | 1024 | 1024-4096 |
| `--fp16` | 启用FP16精度 | false | true |
| `--int8` | 启用INT8精度 | false | false |

### 4. 验证转换结果

```bash
# 检查生成的Engine文件
ls -la sample\models\yolov5s.engine

# 文件大小应该比ONNX文件小
# 典型的YOLOv5s Engine文件大小约为14-20MB
```

## 🚀 Engine推理

### 1. 项目构建

```bash
# 清理之前的构建
xmake clean

# 构建AIFramework库
xmake build AIFramework

# 构建测试程序
xmake build trt_test
```

### 2. 配置测试参数

编辑测试配置文件 `sample/trt_test/example_trt_yolo.h`：

```cpp
int test_yolo()
{
    std::cout << "=== Starting YOLO Test ===" << std::endl;
    
    // 模型路径配置
    std::string modelPath = R"(F:\algo_ai\sample\models\yolov5s.engine)";
    
    // 推理参数
    float confidence_threshold = 0.25f;  // 置信度阈值
    float nms_threshold = 0.25f;         // NMS阈值
    int gpu_id = 0;                      // GPU设备ID
    
    // 测试图片路径
    std::string img_dir = R"(F:\algo_ai\sample\test_data\test\*.jpg)";
    
    // 输出路径
    std::string save_dir = R"(F:\algo_ai\sample\test_data\output\)";
    
    // 创建推理器
    auto infer = Yolo::create_infer(modelPath, Yolo::Type::V5, gpu_id, confidence_threshold);
    
    if (infer == nullptr) {
        std::cout << "ERROR: Can not load model : " << modelPath << std::endl;
        return -1;
    }
    
    // ... 推理代码
}
```

### 3. 执行推理

```bash
# 进入可执行文件目录
cd build\windows\x64\release

# 复制必要的DLL文件（如果需要）
copy "E:\TensorRT-8.6.1.6\lib\*.dll" .
copy "E:\cudnn-windows-x86_64-8.9.0.131_cuda12\lib\x64\*.dll" .

# 运行推理测试
.\trt_test.exe
```

### 4. 推理结果

成功运行后，您将看到类似输出：
```
=== Starting YOLO Test ===
Model path: F:\algo_ai\sample\models\yolov5s.engine
Model file exists, proceeding...
[info] Base device: [ID 0]<NVIDIA GeForce RTX 3090>[arch 8.6][GMEM 22.72 GB/24.00 GB]
[info] Max Batch Size: 1
[info] Inputs: 1
[info]         0.images : shape {1 x 3 x 640 x 640}, Float32
[info] Outputs: 1
[info]         0.output0 : shape {1 x 25200 x 85}, Float32
Model loaded successfully!
Searching for images in: F:\algo_ai\sample\test_data\test\*.jpg
Found 5 images
F:\algo_ai\sample\test_data\output\0.jpg
F:\algo_ai\sample\test_data\output\1.jpg
F:\algo_ai\sample\test_data\output\2.jpg
F:\algo_ai\sample\test_data\output\3.jpg
F:\algo_ai\sample\test_data\output\4.jpg
```

## ⚡ 性能优化

### 1. 模型优化选项

#### FP16精度优化
```bash
# 转换时启用FP16
trtexec --onnx=model.onnx --saveEngine=model_fp16.engine --fp16
```

#### 动态形状优化
```bash
# 支持动态批处理
trtexec --onnx=model.onnx --saveEngine=model_dynamic.engine \
  --minShapes=input:1x3x640x640 \
  --optShapes=input:4x3x640x640 \
  --maxShapes=input:8x3x640x640
```

### 2. 推理优化

#### 批处理优化
```cpp
// 批量推理多张图片
std::vector<cv::Mat> batch_images;
// ... 加载多张图片
auto results = infer->commits(batch_images);
```

#### 内存优化
```cpp
// 预分配内存，避免频繁分配
infer->warmup();  // 预热推理器
```

### 3. 性能监控

查看详细性能日志：
```bash
# 查看最新日志文件
cat build/windows/x64/release/log/AIRuntime_*.log
```

典型性能指标：
- **预处理时间**: 0-158ms
- **推理时间**: 1-37ms  
- **后处理时间**: 0ms
- **总处理时间**: 47ms/图片（平均）
- **GPU内存使用**: ~30MB

## 🔍 常见问题

### 1. 转换问题

**Q: ONNX转Engine失败**
```
A: 检查以下项目：
- ONNX模型是否有效
- TensorRT版本是否兼容
- CUDA版本是否匹配
- 工作空间大小是否足够
```

**Q: Engine文件过大**
```
A: 尝试以下优化：
- 启用FP16精度：--fp16
- 减少工作空间：--workspace=512
- 检查模型是否包含不必要的层
```

### 2. 推理问题

**Q: 找不到DLL文件**
```
A: 复制必要的运行时库：
copy "E:\TensorRT-8.6.1.6\lib\*.dll" build\windows\x64\release\
copy "E:\cudnn-windows-x86_64-8.9.0.131_cuda12\lib\x64\*.dll" build\windows\x64\release\
```

**Q: GPU内存不足**
```
A: 优化策略：
- 减少批处理大小
- 启用FP16精度
- 释放不必要的GPU内存
```

**Q: 推理速度慢**
```
A: 性能优化：
- 确保使用GPU推理
- 启用TensorRT优化
- 预热推理器
- 使用批处理
```

### 3. 调试技巧

#### 启用详细日志
```cpp
// 在代码中启用调试日志
LOG_INFO("详细信息: {}", info);
```

#### 检查模型信息
```cpp
// 打印模型详细信息
std::cout << "Model info: " << infer->infer_info().dump() << std::endl;
```

#### 性能分析
```cpp
// 使用内置计时器
TRT::TimeCost timer;
timer.start();
// ... 推理代码
auto cost = timer.get_cost_time();
std::cout << "推理耗时: " << cost << "ms" << std::endl;
```

## 📝 总结

本指南涵盖了从ONNX模型转换到TensorRT Engine推理的完整流程。关键步骤包括：

1. **环境准备**: 确保所有依赖库正确安装
2. **模型转换**: 使用适当的参数将ONNX转换为Engine
3. **代码配置**: 正确设置路径和推理参数
4. **性能优化**: 根据需求选择合适的优化策略
5. **问题排查**: 使用日志和调试工具解决问题

遵循本指南，您应该能够成功部署和运行TensorRT YOLO推理系统。

## 📚 附录

### A. 完整代码示例

#### A.1 基础推理示例

```cpp
#include "opencv2/opencv.hpp"
#include "../../include/private/trt/trt_models.h"

int basic_yolo_inference() {
    // 1. 配置参数
    std::string modelPath = R"(F:\algo_ai\sample\models\yolov5s.engine)";
    std::string imagePath = R"(F:\algo_ai\sample\test_data\test\test.jpg)";
    std::string outputPath = R"(F:\algo_ai\sample\test_data\output\result.jpg)";

    float confidence_threshold = 0.25f;
    float nms_threshold = 0.45f;
    int gpu_id = 0;

    // 2. 创建推理器
    auto infer = Yolo::create_infer(modelPath, Yolo::Type::V5, gpu_id, confidence_threshold);
    if (!infer) {
        std::cerr << "Failed to create YOLO inferencer" << std::endl;
        return -1;
    }

    // 3. 加载图片
    cv::Mat image = cv::imread(imagePath);
    if (image.empty()) {
        std::cerr << "Failed to load image: " << imagePath << std::endl;
        return -1;
    }

    // 4. 执行推理
    auto result = infer->commit(image);
    auto boxes = result.get();

    // 5. 绘制结果
    for (const auto& box : boxes.boxes) {
        cv::rectangle(image,
                     cv::Point(box.left, box.top),
                     cv::Point(box.right, box.bottom),
                     cv::Scalar(0, 255, 0), 2);

        std::string label = cv::format("%.2f", box.confidence);
        cv::putText(image, label,
                   cv::Point(box.left, box.top - 10),
                   cv::FONT_HERSHEY_SIMPLEX, 0.5,
                   cv::Scalar(0, 255, 0), 2);
    }

    // 6. 保存结果
    cv::imwrite(outputPath, image);

    std::cout << "检测到 " << boxes.boxes.size() << " 个目标" << std::endl;
    std::cout << "推理时间: " << boxes.infer_time << "ms" << std::endl;

    return 0;
}
```

#### A.2 批量推理示例

```cpp
int batch_yolo_inference() {
    std::string modelPath = R"(F:\algo_ai\sample\models\yolov5s.engine)";
    std::string imageDir = R"(F:\algo_ai\sample\test_data\test\)";
    std::string outputDir = R"(F:\algo_ai\sample\test_data\output\)";

    // 创建推理器
    auto infer = Yolo::create_infer(modelPath, Yolo::Type::V5, 0, 0.25f);
    if (!infer) return -1;

    // 加载所有图片
    std::vector<cv::String> imagePaths;
    std::vector<cv::Mat> images;
    cv::glob(imageDir + "*.jpg", imagePaths);

    for (const auto& path : imagePaths) {
        cv::Mat img = cv::imread(path);
        if (!img.empty()) {
            images.push_back(img);
        }
    }

    // 批量推理
    auto results = infer->commits(images);

    // 处理结果
    for (size_t i = 0; i < results.size(); ++i) {
        auto boxes = results[i].get();
        auto resultImg = draw_boxes(images[i], boxes);

        std::string outputPath = outputDir + std::to_string(i) + ".jpg";
        cv::imwrite(outputPath, resultImg);

        std::cout << "图片 " << i << ": 检测到 " << boxes.boxes.size()
                  << " 个目标, 耗时 " << boxes.total_time << "ms" << std::endl;
    }

    return 0;
}
```

### B. API参考

#### B.1 Yolo类主要接口

```cpp
class Yolo {
public:
    enum class Type {
        V5 = 0,    // YOLOv5
        V8 = 1,    // YOLOv8
        V8Seg = 2, // YOLOv8分割
        V8OBB = 3  // YOLOv8旋转框
    };

    // 创建推理器
    static std::shared_ptr<Infer> create_infer(
        const std::string& engine_file,     // Engine文件路径
        Type type,                          // YOLO类型
        int gpuid,                          // GPU设备ID
        float confidence_threshold,         // 置信度阈值
        float nms_threshold = 0.45f,       // NMS阈值
        int max_objects = 1024,            // 最大检测目标数
        bool use_multi_preprocess = false  // 是否使用多线程预处理
    );
};

class Infer {
public:
    // 单张图片推理
    virtual std::shared_future<BoxArray> commit(const cv::Mat& image) = 0;

    // 批量图片推理
    virtual std::vector<std::shared_future<BoxArray>> commits(
        const std::vector<cv::Mat>& images) = 0;

    // 获取推理器信息
    virtual std::string infer_info() = 0;

    // 预热推理器
    virtual void warmup() = 0;
};
```

#### B.2 检测结果结构

```cpp
struct Box {
    float left, top, right, bottom;  // 边界框坐标
    float confidence;                // 置信度
    int class_label;                // 类别标签
    std::string class_name;         // 类别名称
};

struct BoxArray {
    std::vector<Box> boxes;         // 检测框数组
    int image_width, image_height;  // 原图尺寸
    float pre_time;                 // 预处理时间(ms)
    float infer_time;              // 推理时间(ms)
    float host_time;               // 后处理时间(ms)
    float total_time;              // 总时间(ms)
};
```

### C. 工具脚本

#### C.1 批量转换脚本 (convert_models.bat)

```batch
@echo off
setlocal enabledelayedexpansion

set ONNX_DIR=sample\models
set ENGINE_DIR=sample\models
set TRTEXEC=trtexec.exe

echo 开始批量转换ONNX模型到TensorRT Engine...

for %%f in (%ONNX_DIR%\*.onnx) do (
    set "filename=%%~nf"
    echo 转换: !filename!.onnx -> !filename!.engine

    %TRTEXEC% ^
        --onnx=%ONNX_DIR%\!filename!.onnx ^
        --saveEngine=%ENGINE_DIR%\!filename!.engine ^
        --explicitBatch ^
        --workspace=2048 ^
        --fp16 ^
        --verbose

    if !errorlevel! equ 0 (
        echo 成功转换: !filename!.engine
    ) else (
        echo 转换失败: !filename!.onnx
    )
    echo.
)

echo 批量转换完成！
pause
```

#### C.2 性能测试脚本 (benchmark.cpp)

```cpp
#include <chrono>
#include <iostream>
#include <vector>

class PerformanceBenchmark {
public:
    static void benchmark_inference(const std::string& engine_path,
                                  const std::string& test_image,
                                  int iterations = 100) {
        auto infer = Yolo::create_infer(engine_path, Yolo::Type::V5, 0, 0.25f);
        if (!infer) {
            std::cerr << "Failed to load model" << std::endl;
            return;
        }

        cv::Mat image = cv::imread(test_image);
        if (image.empty()) {
            std::cerr << "Failed to load test image" << std::endl;
            return;
        }

        // 预热
        std::cout << "预热推理器..." << std::endl;
        for (int i = 0; i < 10; ++i) {
            infer->commit(image).get();
        }

        // 性能测试
        std::cout << "开始性能测试 (" << iterations << " 次推理)..." << std::endl;

        auto start = std::chrono::high_resolution_clock::now();

        std::vector<float> inference_times;
        for (int i = 0; i < iterations; ++i) {
            auto result = infer->commit(image);
            auto boxes = result.get();
            inference_times.push_back(boxes.total_time);
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

        // 统计结果
        float avg_time = std::accumulate(inference_times.begin(), inference_times.end(), 0.0f) / iterations;
        float min_time = *std::min_element(inference_times.begin(), inference_times.end());
        float max_time = *std::max_element(inference_times.begin(), inference_times.end());

        std::cout << "\n=== 性能测试结果 ===" << std::endl;
        std::cout << "总测试时间: " << total_time << "ms" << std::endl;
        std::cout << "平均推理时间: " << avg_time << "ms" << std::endl;
        std::cout << "最快推理时间: " << min_time << "ms" << std::endl;
        std::cout << "最慢推理时间: " << max_time << "ms" << std::endl;
        std::cout << "推理吞吐量: " << (1000.0f / avg_time) << " FPS" << std::endl;
    }
};
```

### D. 部署清单

#### D.1 运行时依赖文件

```
部署目录/
├── 可执行文件
│   ├── your_app.exe
│   └── AIFramework.dll
├── TensorRT运行时
│   ├── nvinfer.dll
│   ├── nvinfer_plugin.dll
│   ├── nvonnxparser.dll
│   └── nvparsers.dll
├── CUDA运行时
│   ├── cudart64_12.dll
│   ├── cublas64_12.dll
│   └── cublasLt64_12.dll
├── cuDNN运行时
│   ├── cudnn64_8.dll
│   ├── cudnn_cnn_infer64_8.dll
│   └── cudnn_ops_infer64_8.dll
├── OpenCV运行时
│   └── opencv_world481.dll
├── 其他依赖
│   ├── fmt.dll
│   └── msvcp140.dll
└── 模型文件
    └── your_model.engine
```

#### D.2 环境变量设置

```batch
# 添加到系统PATH
set PATH=%PATH%;E:\TensorRT-8.6.1.6\lib
set PATH=%PATH%;E:\cudnn-windows-x86_64-8.9.0.131_cuda12\lib\x64
set PATH=%PATH%;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin
```

这份完整的文档现在包含了从ONNX转换到Engine推理的所有必要信息，包括详细的代码示例、API参考、工具脚本和部署指南。
