#include <string.h>
#include <iostream>
#include "CPlugin.h"
#include "CService.h"

IPlugin* CService::CreatePlugin(const char* pstr<PERSON><PERSON>, IPlugin* pIHost)
{
    IPlugin* pIPlugin = NULL;
    pIPlugin = new CPlugin(pstrKey, pIHost);
    return pIPlugin;
}

void CService::DestoryPlugin(IPlugin* pIPlugin)
{
    CPlugin* pPlugin = dynamic_cast<CPlugin*>(pIPlugin);
    if (NULL != pPlugin)
    {
        delete pPlugin;
    }
    return ;
}

/*!<	插件服务实例对象	*/
static CService s_theService;
extern "C"  __declspec(dllexport) IPluginService* GetIService()
{
    return (&s_theService);
}
