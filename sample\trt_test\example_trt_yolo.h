

#include "opencv2/opencv.hpp"
#include <vector>

#include "../../include/private/airuntime/logger.h"
#include "../../include/private/trt/trt_common/time_cost.h"
#include "../../include/private/trt/trt_models.h"
#include "./trt_test_utility.h"

int test_yolo()
{
    std::cout << "=== Starting YOLO Test ===" << std::endl;
    /*
    const std::string& engine_file, int gpuid, 
    float confidence_threshold, float nms_threshold, int max_objects, std::vector<std::vector<int>> dims*/

    std::string testDir   = R"(G:\algo_ai - 副本\sample\models\)";
    std::string modelPath = testDir + "yolov5s.engine";
    // std::string modelPath = testDir + "yolov8s.engine";
    float       nms_thr   = 0.25f;
    
    std::cout << "Model path: " << modelPath << std::endl;

    auto infer = Yolo::create_infer(modelPath, Yolo::Type::V5, 0, 0.25);
    // auto infer = yolo8::create_infer(modelPath, 0, 0.25);

    if (infer == nullptr) {
        std::cout << "ERROR: Can not load model : " << modelPath << std::endl;
        LOG_INFOE("Can not load model : {}", modelPath);
        return -1;
    }
    std::cout << "Model loaded successfully!" << std::endl;
    LOG_INFO("Load model successful! {}", modelPath);

    std::string             img_dir = R"(G:\algo_ai - 副本\sample\test_data\test\*.jpg)";
    std::vector<cv::String> img_paths;
    std::vector<cv::Mat>    mat_lists;
    std::cout << "Searching for images in: " << img_dir << std::endl;
    cv::glob(img_dir, img_paths);
    std::cout << "Found " << img_paths.size() << " images" << std::endl;
    if (img_paths.empty()) {
        std::cout << "ERROR: Not found any image from : " << img_dir << std::endl;
        LOG_INFO("Not found any image from : {}", img_dir);
        return -1;
    }
    for (auto path : img_paths) {
        mat_lists.emplace_back(cv::imread(path));
    }
    TRT::TimeCost time_cost;
    time_cost.start();
    auto        rstlists = infer->commits(mat_lists);
    int         cnt      = 0;
    std::string save_dir = R"(G:\algo_ai - 副本\sample\test_data\output\)";
    std::string save_name;
    for (auto item : rstlists) {
        auto boxes  = item.get();
        auto rstMat = draw_rst(mat_lists[cnt], boxes);
        // char save_name[200];
        // sprintf(save_name, "%s%d.jpg", save_dir.c_str(), cnt);
        
        save_name = save_dir + std::to_string(cnt) + ".jpg";
        std::cout << save_name << std::endl;
        cv::imwrite(save_name, rstMat);
        cnt++;
        LOG_INFO("pre_time: {}, infer_time: {}, host_time: {}, total: {}", boxes.pre_time, boxes.infer_time, boxes.host_time, boxes.total_time);
    }
    long long total_cost   = time_cost.get_cost_time();
    double    per_cost_img = total_cost / mat_lists.size();
    LOG_INFO("===================summary=====================");
    LOG_INFO("total cost: {}, cnt: {}, per_cost_img: {} ms", total_cost, mat_lists.size(), per_cost_img);
    LOG_INFO("resource cost: {}", infer->infer_info().dump());
}