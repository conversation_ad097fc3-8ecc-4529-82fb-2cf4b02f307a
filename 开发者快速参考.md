# AIRuntime 开发者快速参考

## 常用 API 速查

### 基础接口

```cpp
// 获取运行时实例
AIRuntimeInterface* GetAIRuntime();

// 初始化运行时
eAIErrorCode InitRuntime(const stAIConfigInfo& cfg);

// 销毁运行时
eAIErrorCode DestoryRuntime();

// 创建模型
eAIErrorCode CreateModle(stAIModelInfo& modelInfo);
eAIErrorCode CreateModle(json& modelInfo);

// 更新模型
eAIErrorCode UpdateModle(stAIModelInfo& newModelInfo);
eAIErrorCode UpdateModleParam(const json& newModelInfo);

// 销毁模型
eAIErrorCode DestroyModle(int modelID);

// 获取模型信息
stAIModelInfo::mPtr GetModelInfo(int modelId);

// 推理接口
eAIErrorCode CommitInferTask(TaskInfoPtr spTaskInfo);  // 异步推理
ModelResultPtr RunInferTask(TaskInfoPtr spTaskInfo);   // 同步推理

// 回调管理
eAIErrorCode RegisterResultListener(int modelID, IModelResultListener* resultListener);
eAIErrorCode UnregisterResultListener(IModelResultListener* resultListener);

// 获取 GPU 信息
stGPUInfo GetGPUInfo(int modelID);

// 模型编译
bool build_model(int model_type, int max_batch_size, const char* onnx_path, 
                 const char* model_save_path, const size_t max_work_space_size);
```

### 数据结构速查

#### 配置信息
```cpp
struct stAIConfigInfo {
    bool    usePinMemory = true;           // 使用固定内存
    size_t  workSpaceSize = 2048;          // 工作空间大小(MB)
    size_t  GPUCachSize = 1024;            // GPU缓存大小(MB)
    size_t  CPUCachSize = 1024;            // CPU缓存大小(MB)
    int     preProcessThreadCnt = 8;       // 预处理线程数
    int     preProcessThreadPriority = 1;  // 预处理线程优先级
    int     inferThreadCnt = 8;            // 推理线程数
    int     inferThreadPriority = 1;       // 推理线程优先级
};
```

#### 模型信息
```cpp
struct stAIModelInfo {
    int modelVersion = 1;                  // 模型版本
    int modelId = 0;                       // 模型ID
    eAIAlgoType algoType = CLASSIFY;       // 算法类型
    std::string modelName = "";            // 模型名称
    std::string modelPath = "";            // 模型文件路径
    stAIInferParam inferParam;             // 推理参数
    std::string modelBackend = "tensorrt"; // 推理后端
    std::string modleLabelPath = "";       // 标签文件路径
    std::vector<int> dims;                 // 模型输入维度
};
```

#### 推理参数
```cpp
struct stAIInferParam {
    int gpuId = 0;                         // GPU ID
    int maxBatchSize = 1;                  // 最大批处理大小
    float confidenceThreshold = 0.0;       // 置信度阈值
    float nmsThreshold = 1.0;              // NMS阈值
    int maxObjectNums = 1024;              // 最大目标数量
    
    // OCR 特定参数
    bool enableDetMat = false;             // 启用检测矩阵
    bool useDilat = false;                 // 使用膨胀
    int kernelSize = 2;                    // 核大小
    float det_db_box_thresh = 0.4;         // DB检测框阈值
    float det_db_unclip_ratio = 1.5;       // DB展开比例
    int max_side_len = 960;                // 最大边长
    
    // 分割特定参数
    float segThreshold = 0.5;              // 分割阈值
    bool maxAreaCont = true;               // 最大面积轮廓
    
    // 动态尺寸参数
    int optBatchSize = 1;                  // 优化批大小
    int optChannalSize = -1;               // 优化通道数
    int optWidthSize = -1;                 // 优化宽度
    int optHeightSize = -1;                // 优化高度
    std::vector<std::vector<int>> dim;     // 动态维度
};
```

#### 任务信息
```cpp
struct stTaskInfo {
    int modelId;                           // 模型ID
    int taskId;                            // 任务ID
    int orgImageId;                        // 原始图像ID
    TimeCost tt;                           // 时间统计
    long long preCostTime;                 // 预处理耗时
    long long inferCostTime;               // 推理耗时
    long long hostCostTime;                // 主机处理耗时
    long long totalCostTime;               // 总耗时
    std::shared_ptr<void> inspParam;       // 检查参数
    std::vector<cv::Mat> imageData;        // 图像数据
    void* promiseResult;                   // Promise结果指针
};
```

#### 结果信息
```cpp
struct stResultItem {
    int code;                              // 类别代码
    int shape;                             // 形状
    float confidence;                      // 置信度
    std::vector<stPoint> points;           // 关键点
    float angle;                           // 角度(OBB)
    
    // OCR 结果
    std::vector<std::pair<std::string, double>> rec_conf_res;  // 识别置信度
    std::vector<float> res_ratio_res;                          // 比例结果
    std::vector<std::vector<cv::Point>> char_single_pos;       // 字符位置
    std::vector<int> ocr_char_index;                           // 字符索引
    std::string ocr_str = "";                                  // 识别文字
    cv::Mat ocr_det;                                           // 检测结果图
    
    // 其他结果
    cv::Mat msae_img;                                          // MSAE图像
    cv::Mat heatMap;                                           // 热力图
    std::vector<std::vector<cv::Point>> mask;                  // 分割掩码
};

struct stModelResult {
    std::shared_ptr<stTaskInfo> taskInfo;                      // 任务信息
    std::vector<std::vector<stResultItem>> itemList;           // 结果列表
};
```

## 算法类型对照表

| 枚举值 | 算法类型 | 描述 | 输入 | 输出 |
|--------|----------|------|------|------|
| CLASSIFY | 图像分类 | 对整张图像进行分类 | 单张图像 | 类别ID和置信度 |
| YOLOV5 | YOLOv5检测 | 目标检测算法 | 单张图像 | 边界框、类别、置信度 |
| YOLO8 | YOLOv8检测 | 改进的目标检测算法 | 单张图像 | 边界框、类别、置信度 |
| YOLO8_OBB | YOLOv8旋转框 | 带角度的目标检测 | 单张图像 | 旋转边界框、类别、置信度 |
| YOLOV8_SEG | YOLOv8分割 | 实例分割算法 | 单张图像 | 边界框、掩码、类别 |
| OCR_DET | OCR文字检测 | 检测图像中的文字区域 | 单张图像 | 文字区域四边形坐标 |
| OCR_REC | OCR文字识别 | 识别文字区域的内容 | 文字区域图像 | 识别文字和置信度 |
| OCR_CLS | OCR方向分类 | 判断文字方向 | 文字区域图像 | 方向类别和置信度 |
| SEGMENT | 语义分割 | 像素级分割 | 单张图像 | 分割掩码 |
| ANOMALIB | 异常检测 | 工业异常检测 | 单张图像 | 异常分数和热力图 |
| MSAE | MSAE算法 | 特定检测算法 | 单张图像 | 检测结果和图像 |
| DEIM | DEIM算法 | 特定检测算法 | 单张图像 | 检测结果 |

## 错误码对照表

| 错误码 | 名称 | 描述 | 解决方案 |
|--------|------|------|----------|
| E_OK | 成功 | 操作成功完成 | - |
| E_OUT_OF_MEMORY | 内存不足 | 系统内存或GPU内存不足 | 减少批处理大小或释放其他内存 |
| E_CREATE_MODEL_FAILED | 模型创建失败 | 模型文件损坏或不兼容 | 检查模型文件和推理后端 |
| E_FILE_NOT_EXIST | 文件不存在 | 模型文件路径错误 | 检查文件路径是否正确 |
| E_QUEUUE_FULL | 队列满 | 任务队列已满 | 等待或增加队列大小 |

## 常用代码模板

### 1. 基础初始化模板
```cpp
#include "AIRuntimeInterface.h"

class AIRuntimeManager {
private:
    AIRuntimeInterface* runtime_;
    
public:
    AIRuntimeManager() : runtime_(GetAIRuntime()) {}
    
    bool Initialize() {
        stAIConfigInfo config;
        config.preProcessThreadCnt = 8;
        config.inferThreadCnt = 4;
        config.usePinMemory = true;
        
        return runtime_->InitRuntime(config) == E_OK;
    }
    
    ~AIRuntimeManager() {
        if (runtime_) {
            runtime_->DestoryRuntime();
        }
    }
};
```

### 2. 模型创建模板
```cpp
bool CreateYOLOModel(int modelId, const std::string& modelPath) {
    stAIModelInfo modelInfo;
    modelInfo.modelId = modelId;
    modelInfo.modelName = "yolo_detector";
    modelInfo.modelPath = modelPath;
    modelInfo.modelBackend = "tensorrt";
    modelInfo.algoType = YOLO8;
    
    // 设置推理参数
    modelInfo.inferParam.confidenceThreshold = 0.5f;
    modelInfo.inferParam.nmsThreshold = 0.4f;
    modelInfo.inferParam.maxObjectNums = 1000;
    modelInfo.inferParam.gpuId = 0;
    
    return GetAIRuntime()->CreateModle(modelInfo) == E_OK;
}
```

### 3. 同步推理模板
```cpp
ModelResultPtr SyncInference(int modelId, const cv::Mat& image) {
    TaskInfoPtr task = std::make_shared<stTaskInfo>();
    task->modelId = modelId;
    task->taskId = static_cast<int>(std::time(nullptr));
    task->imageData = {image};
    
    return GetAIRuntime()->RunInferTask(task);
}
```

### 4. 异步推理模板
```cpp
class AsyncInferenceHandler : public IModelResultListener {
public:
    void OnModelResult(ModelResultPtr result) override {
        std::cout << "收到异步推理结果, 任务ID: " 
                  << result->taskInfo->taskId << std::endl;
        
        // 处理结果
        ProcessResult(result);
    }
    
private:
    void ProcessResult(ModelResultPtr result) {
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                // 处理每个检测结果
                std::cout << "类别: " << item.code 
                          << ", 置信度: " << item.confidence << std::endl;
            }
        }
    }
};

// 使用异步推理
void AsyncInference(int modelId, const cv::Mat& image) {
    static AsyncInferenceHandler handler;
    GetAIRuntime()->RegisterResultListener(modelId, &handler);
    
    TaskInfoPtr task = std::make_shared<stTaskInfo>();
    task->modelId = modelId;
    task->taskId = static_cast<int>(std::time(nullptr));
    task->imageData = {image};
    
    GetAIRuntime()->CommitInferTask(task);
}
```

### 5. OCR 完整流程模板
```cpp
class OCRProcessor {
private:
    int det_model_id_ = 1;  // 检测模型ID
    int rec_model_id_ = 2;  // 识别模型ID
    
public:
    bool Initialize() {
        // 创建检测模型
        stAIModelInfo detModel;
        detModel.modelId = det_model_id_;
        detModel.algoType = OCR_DET;
        detModel.modelPath = "ocr_det.trtmodel";
        detModel.modelBackend = "tensorrt";
        
        // 创建识别模型
        stAIModelInfo recModel;
        recModel.modelId = rec_model_id_;
        recModel.algoType = OCR_REC;
        recModel.modelPath = "ocr_rec.trtmodel";
        recModel.modleLabelPath = "ppocr_keys_v1.txt";
        recModel.modelBackend = "tensorrt";
        
        auto runtime = GetAIRuntime();
        return runtime->CreateModle(detModel) == E_OK && 
               runtime->CreateModle(recModel) == E_OK;
    }
    
    std::vector<std::string> ProcessImage(const cv::Mat& image) {
        std::vector<std::string> results;
        
        // 1. 文字检测
        auto detResult = SyncInference(det_model_id_, image);
        if (!detResult || detResult->itemList.empty()) {
            return results;
        }
        
        // 2. 文字识别
        for (const auto& itemList : detResult->itemList) {
            for (const auto& item : itemList) {
                if (!item.ocr_det.empty()) {
                    auto recResult = SyncInference(rec_model_id_, item.ocr_det);
                    if (recResult && !recResult->itemList.empty()) {
                        for (const auto& recItemList : recResult->itemList) {
                            for (const auto& recItem : recItemList) {
                                if (!recItem.ocr_str.empty()) {
                                    results.push_back(recItem.ocr_str);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return results;
    }
};
```

### 6. 批处理推理模板
```cpp
ModelResultPtr BatchInference(int modelId, const std::vector<cv::Mat>& images) {
    TaskInfoPtr task = std::make_shared<stTaskInfo>();
    task->modelId = modelId;
    task->taskId = static_cast<int>(std::time(nullptr));
    task->imageData = images;  // 批量图像
    
    return GetAIRuntime()->RunInferTask(task);
}
```

### 7. 性能监控模板
```cpp
class PerformanceMonitor {
public:
    void LogPerformance(ModelResultPtr result) {
        auto taskInfo = result->taskInfo;
        
        std::cout << "=== 性能统计 ===" << std::endl;
        std::cout << "任务ID: " << taskInfo->taskId << std::endl;
        std::cout << "预处理时间: " << taskInfo->preCostTime << "ms" << std::endl;
        std::cout << "推理时间: " << taskInfo->inferCostTime << "ms" << std::endl;
        std::cout << "后处理时间: " << taskInfo->hostCostTime << "ms" << std::endl;
        std::cout << "总时间: " << taskInfo->totalCostTime << "ms" << std::endl;
        std::cout << "===============" << std::endl;
    }
};
```

## 调试技巧

### 1. 日志级别设置
```cpp
// 在代码中使用不同级别的日志
LOG_INFO("正常信息日志");
LOG_INFOW("警告日志");
LOG_INFOE("错误日志");
LOG_INFOD("调试日志");
```

### 2. 内存使用监控
```cpp
stGPUInfo gpuInfo = GetAIRuntime()->GetGPUInfo(modelId);
std::cout << "GPU " << gpuInfo.gpuId << " 内存使用情况:" << std::endl;
std::cout << "总内存: " << gpuInfo.totalMemorySize << " MB" << std::endl;
std::cout << "已使用: " << gpuInfo.usedMemorySize << " MB" << std::endl;
std::cout << "可用: " << gpuInfo.avaliableMemorySize << " MB" << std::endl;
std::cout << "利用率: " << gpuInfo.gpuUtilRate << "%" << std::endl;
```

### 3. 错误处理最佳实践
```cpp
eAIErrorCode result = GetAIRuntime()->CreateModle(modelInfo);
switch (result) {
    case E_OK:
        std::cout << "模型创建成功" << std::endl;
        break;
    case E_FILE_NOT_EXIST:
        std::cerr << "错误: 模型文件不存在 - " << modelInfo.modelPath << std::endl;
        break;
    case E_CREATE_MODEL_FAILED:
        std::cerr << "错误: 模型创建失败，请检查模型格式和推理后端" << std::endl;
        break;
    case E_OUT_OF_MEMORY:
        std::cerr << "错误: 内存不足，请释放其他资源或减少批处理大小" << std::endl;
        break;
    default:
        std::cerr << "未知错误: " << result << std::endl;
}
```

## 构建命令速查

```bash
# 配置构建环境
xmake f --vs=2022 --vs_toolset=14.38  # Windows
xmake f                                # Linux

# 编译项目
xmake                                  # Debug 版本
xmake f -m release && xmake           # Release 版本

# 运行测试
xmake run test_dll                     # DLL 测试
xmake run trt_test                     # TensorRT 测试

# 清理构建
xmake clean                            # 清理编译文件
xmake clean --all                      # 清理所有文件

# 生成 Visual Studio 项目
xmake project -k vsxmake              # 生成 VS 项目文件
```

## 常见问题快速解决

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 模型加载失败 | 文件路径错误或文件损坏 | 检查路径和文件完整性 |
| GPU 内存不足 | 模型太大或批处理过大 | 减少批处理大小或使用更大显存的GPU |
| 推理速度慢 | 未使用GPU或配置不当 | 检查CUDA环境和GPU配置 |
| 编译错误 | 依赖库缺失或版本不匹配 | 检查第三方库版本和路径 |
| 结果不准确 | 预处理或后处理参数错误 | 检查置信度阈值和NMS参数 |

---

本快速参考指南提供了 AIRuntime 开发中最常用的 API、数据结构和代码模板，帮助开发者快速上手和解决常见问题。