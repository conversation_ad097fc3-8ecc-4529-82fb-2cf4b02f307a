# YOLO TensorRT 推理快速指南

## 🚀 一键开始

### 方法1：使用快速脚本（推荐）
```bash
# 双击运行
快速开始脚本.bat
```

### 方法2：手动执行
```bash
# 1. 构建项目
xmake clean
xmake build AIFramework
xmake build onnx2engine  
xmake build trt_test

# 2. ONNX转Engine（如果有yolov5s.onnx）
.\build\windows\x64\release\onnx2engine.exe sample\models\yolov5s.onnx sample\models\yolov5s.engine --fp16

# 3. 运行推理测试
cd build\windows\x64\release
.\trt_test.exe
```

## 📁 目录结构
```
F:\algo_ai\
├── sample\
│   ├── models\
│   │   ├── yolov5s.onnx      # 输入：ONNX模型
│   │   └── yolov5s.engine    # 输出：TensorRT引擎
│   ├── test_data\
│   │   ├── test\             # 输入：测试图片
│   │   └── output\           # 输出：检测结果
│   └── trt_test\             # 测试代码
├── build\windows\x64\release\
│   ├── trt_test.exe          # 推理测试程序
│   ├── onnx2engine.exe       # 转换工具
│   └── *.dll                 # 运行时库
└── 快速开始脚本.bat           # 一键操作脚本
```

## ⚡ 核心命令

### ONNX转Engine
```bash
# 基础转换
onnx2engine.exe input.onnx output.engine

# FP16优化（推荐）
onnx2engine.exe input.onnx output.engine --fp16

# 自定义参数
onnx2engine.exe input.onnx output.engine --fp16 --batch 4 --workspace 2048
```

### 推理测试
```bash
# 确保在正确目录
cd build\windows\x64\release

# 运行测试（会自动处理test目录下的所有jpg图片）
.\trt_test.exe
```

## 📊 预期结果

### 成功输出示例
```
=== Starting YOLO Test ===
Model path: F:\algo_ai\sample\models\yolov5s.engine
Model file exists, proceeding...
[info] Base device: [ID 0]<NVIDIA GeForce RTX 3090>
[info] Max Batch Size: 1
[info] Inputs: 1
[info]         0.images : shape {1 x 3 x 640 x 640}, Float32
[info] Outputs: 1
[info]         0.output0 : shape {1 x 25200 x 85}, Float32
Model loaded successfully!
Found 5 images
F:\algo_ai\sample\test_data\output\0.jpg
F:\algo_ai\sample\test_data\output\1.jpg
...
```

### 性能指标
- **推理时间**: 1-37ms（首次较慢，后续很快）
- **平均处理时间**: ~47ms/图片
- **GPU内存使用**: ~30MB
- **吞吐量**: ~21 FPS

## 🔧 常见问题

### Q1: 找不到DLL文件
```bash
# 解决方案：复制运行时库
copy "E:\TensorRT-8.6.1.6\lib\*.dll" build\windows\x64\release\
copy "E:\cudnn-windows-x86_64-8.9.0.131_cuda12\lib\x64\*.dll" build\windows\x64\release\
```

### Q2: 模型加载失败
```bash
# 检查文件路径
dir sample\models\yolov5s.engine

# 检查文件权限
# 确保Engine文件完整且未损坏
```

### Q3: 没有测试图片
```bash
# 添加测试图片到以下目录
sample\test_data\test\*.jpg
```

### Q4: 构建失败
```bash
# 清理重新构建
xmake clean
xmake f --vs=2022 --vs_toolset=14.38
xmake
```

## 📈 性能优化建议

1. **使用FP16精度**: 转换时添加 `--fp16` 参数
2. **批处理**: 如果有多张图片，使用批处理模式
3. **预热**: 首次推理较慢，后续会很快
4. **GPU选择**: 确保使用性能最好的GPU

## 🎯 下一步

1. **查看详细文档**: `TensorRT_YOLO_推理指南.md`
2. **自定义模型**: 替换为您自己的ONNX模型
3. **集成到应用**: 参考API文档集成到您的项目
4. **性能调优**: 根据具体需求调整参数

## 📞 技术支持

如果遇到问题：
1. 查看日志文件：`build\windows\x64\release\log\*.log`
2. 检查GPU驱动和CUDA版本
3. 确认所有依赖库正确安装
4. 参考完整文档获取更多信息

---

**快速开始就是这么简单！** 🎉
