# AIRuntime 项目设计模式分析

本文档深入分析 AIRuntime 项目中使用的设计模式，结合具体代码实现，帮助开发者理解项目的架构设计思想。

## 目录

- [设计模式概览](#设计模式概览)
- [创建型模式](#创建型模式)
- [结构型模式](#结构型模式)
- [行为型模式](#行为型模式)
- [并发模式](#并发模式)
- [架构模式](#架构模式)
- [设计模式应用总结](#设计模式应用总结)

## 设计模式概览

AIRuntime 项目采用了多种经典设计模式，形成了一个高内聚、低耦合的架构体系：

```mermaid
graph TB
    subgraph "创建型模式"
        A1[单例模式<br/>Singleton]
        A2[工厂方法模式<br/>Factory Method]
        A3[抽象工厂模式<br/>Abstract Factory]
    end
    
    subgraph "结构型模式"
        B1[外观模式<br/>Facade]
        B2[适配器模式<br/>Adapter]
        B3[桥接模式<br/>Bridge]
    end
    
    subgraph "行为型模式"
        C1[观察者模式<br/>Observer]
        C2[策略模式<br/>Strategy]
        C3[模板方法模式<br/>Template Method]
        C4[命令模式<br/>Command]
    end
    
    subgraph "并发模式"
        D1[生产者-消费者模式<br/>Producer-Consumer]
        D2[线程池模式<br/>Thread Pool]
        D3[Future/Promise模式<br/>Future-Promise]
    end
    
    subgraph "架构模式"
        E1[分层架构<br/>Layered Architecture]
        E2[插件架构<br/>Plugin Architecture]
    end
```

## 创建型模式

### 1. 单例模式 (Singleton Pattern)

**应用场景**: AIRuntime 核心类确保全局唯一实例

**代码实现**:
```cpp
// include/private/airuntime/AIRuntimeDefines.h
#define DECLARE_SINGLETON(class_name) \
    static class_name* get_instance() { \
        static class_name instance; \
        return &instance; \
    }

#define HIDE_CREATE_METHODS(class_name) \
    class_name(); \
    ~class_name(); \
    class_name(const class_name&) = delete; \
    class_name& operator=(const class_name&) = delete;

// include/private/airuntime/AIRuntime.h
class AIRuntime : public AIRuntimeInterface {
public:
    DECLARE_SINGLETON(AIRuntime)  // 声明单例
    
private:
    HIDE_CREATE_METHODS(AIRuntime);  // 隐藏构造函数
};

// src/airuntime/AIRuntimeInterface.cpp
AIRuntimeInterface* GetAIRuntime() {
    auto obj = AIRuntime::get_instance();  // 获取单例实例
    return obj;
}
```

**设计优势**:
- **全局访问点**: 提供统一的运行时访问接口
- **资源控制**: 确保系统中只有一个 AIRuntime 实例
- **线程安全**: 使用 C++11 的静态局部变量保证线程安全
- **延迟初始化**: 第一次调用时才创建实例

### 2. 工厂方法模式 (Factory Method Pattern)

**应用场景**: 创建不同类型的推理引擎实例

**代码实现**:
```cpp
// 抽象产品 - include/private/airuntime/inference.h
namespace Algo {
    class Infer {
    public:
        virtual std::shared_future<BoxArray> commit(const cv::Mat& image) = 0;
        virtual std::vector<std::shared_future<BoxArray>> commits(const std::vector<cv::Mat>& images) = 0;
        virtual json infer_info() = 0;
        virtual bool set_param(const json& config) = 0;
    };
}

// 具体工厂方法 - TensorRT 后端
void AIRuntime::create_trt_model(stAIModelInfo modelInfo, std::shared_ptr<Algo::Infer>& infer) {
    switch (modelInfo.algoType) {
        case CLASSIFY:
            infer = Classification::create_infer(modelInfo.modelPath, 
                                               modelInfo.inferParam.gpuId, 
                                               modelInfo.inferParam.confidenceThreshold, 
                                               modelInfo.inferParam.dim);
            break;
        case YOLOV5:
            infer = Yolo::create_infer(modelInfo.modelPath, Yolo::Type::V5, 
                                     modelInfo.inferParam.gpuId, 
                                     modelInfo.inferParam.confidenceThreshold, 
                                     modelInfo.inferParam.nmsThreshold);
            break;
        case YOLO8:
            infer = yolo8::create_infer(modelInfo.modelPath, 
                                      modelInfo.inferParam.gpuId, 
                                      modelInfo.inferParam.confidenceThreshold, 
                                      modelInfo.inferParam.nmsThreshold, 
                                      modelInfo.inferParam.maxObjectNums);
            break;
        // ... 更多算法类型
    }
}
```

**设计优势**:
- **解耦创建逻辑**: 将对象创建与使用分离
- **易于扩展**: 添加新算法类型只需扩展 switch 语句
- **统一接口**: 所有推理引擎都实现相同的 `Algo::Infer` 接口

## 结构型模式

### 1. 外观模式 (Facade Pattern)

**应用场景**: AIRuntimeInterface 为复杂的推理系统提供简化接口

**代码实现**:
```cpp
// 外观接口 - include/public/AIRuntimeInterface.h
class AIRuntimeInterface {
public:
    // 简化的初始化接口
    virtual eAIErrorCode InitRuntime(const stAIConfigInfo& cfg) = 0;
    
    // 简化的模型管理接口
    virtual eAIErrorCode CreateModle(stAIModelInfo& modelInfo) = 0;
    virtual eAIErrorCode CreateModle(json& modelInfo) = 0;
    
    // 简化的推理接口
    virtual eAIErrorCode CommitInferTask(TaskInfoPtr spTaskInfo) = 0;
    virtual ModelResultPtr RunInferTask(TaskInfoPtr spTaskInfo) = 0;
    
    // 简化的回调管理接口
    virtual eAIErrorCode RegisterResultListener(int modelID, IModelResultListener* resultListener) = 0;
};

// 外观实现 - 隐藏复杂的内部实现
class AIRuntime : public AIRuntimeInterface {
private:
    // 复杂的内部组件
    rigtorp::MPMCQueue<TaskInfoPtr> m_queueTask;
    rigtorp::MPMCQueue<ModelResultPtr> m_queueResult;
    rigtorp::MPMCQueue<InferResultPtr> m_rstResult;
    
    std::thread mInferThread;
    std::thread mCallbackThread;
    std::thread mRstListenerThread;
    
    std::map<int, std::shared_ptr<Algo::Infer>> m_modelMap;
    std::map<int, stAIModelInfo> m_model_param;
};
```

**设计优势**:
- **简化接口**: 用户只需关心高层接口，无需了解内部复杂性
- **解耦客户端**: 客户端代码与复杂的内部实现解耦
- **易于使用**: 提供直观、易用的 API

## 行为型模式

### 1. 观察者模式 (Observer Pattern)

**应用场景**: 推理结果的异步通知机制

**代码实现**:
```cpp
// 观察者接口 - include/public/AIRuntimeInterface.h
class IModelResultListener {
public:
    virtual void OnModelResult(ModelResultPtr spResult) = 0;
};

// 主题类 - AIRuntime 管理观察者列表
class AIRuntime : public AIRuntimeInterface {
private:
    std::vector<IModelResultListener*> m_callbackList;  // 观察者列表
    
public:
    // 注册观察者
    eAIErrorCode RegisterResultListener(int modelID, IModelResultListener* resultListener) override {
        m_callbackList.push_back(resultListener);
        return E_OK;
    }
    
    // 通知所有观察者
    void CallbackWorker() {
        while (true) {
            ModelResultPtr modelResult;
            bool found = m_queueResult.try_pop(modelResult);
            
            if (found) {
                // 通知所有观察者
                for (int i = 0; i < m_callbackList.size(); i++) {
                    m_callbackList[i]->OnModelResult(modelResult);
                }
            }
        }
    }
};

// 具体观察者实现 - sample/dll_test/test_main.h
class PR : public IModelResultListener {
public: 
    void OnModelResult(ModelResultPtr spResult) override {
        for(int i = 0; i < spResult->itemList.size(); i++) {
            for (int j = 0; j < spResult->itemList[i].size(); j++) {
                std::string ret = (spResult->itemList[i])[j].Info();
                std::cout << ret << std::endl;
            }
        }
    }
};
```

**设计优势**:
- **松耦合**: 主题和观察者之间松耦合
- **动态关系**: 可以在运行时动态添加或删除观察者
- **广播通信**: 一次通知可以更新多个观察者

### 2. 策略模式 (Strategy Pattern)

**应用场景**: 不同算法类型的结果处理策略

**代码实现**:
```cpp
// 在 AIRuntime::ToRuntimeResult 中体现策略模式
std::vector<stResultItem> AIRuntime::ToRuntimeResult(Algo::BoxArray& boxs, TaskInfoPtr taskInfo) {
    std::vector<stResultItem> rst_list;
    for (const Algo::Box& box : boxs) {
        stResultItem rst;
        // 根据算法类型选择不同的处理策略
        switch (m_model_param[taskInfo->modelId].algoType) {
            case CLASSIFY:
                // 分类策略
                rst.code = box.class_label;
                rst.confidence = box.confidence;
                break;
                
            case YOLOV5:
            case YOLO8:
                // YOLO 检测策略
                rst.code = box.class_label;
                rst.confidence = box.confidence;
                rst.points.push_back(stPoint(box.left, box.top));
                rst.points.push_back(stPoint(box.right, box.bottom));
                break;
                
            case OCR_REC:
                // OCR 识别策略
                rst.confidence = box.confidence;
                if (box.ocr_ret_str != "") {
                    rst.confidence = box.ocr_ret_score;
                    rst.ocr_str = box.ocr_ret_str;
                }
                break;
                
            case YOLOV8_SEG:
                // 分割策略
                rst.confidence = box.confidence;
                if (box.contour.size() > 0) {
                    rst.mask.push_back(box.contour);
                }
                rst.points.push_back(stPoint(box.left, box.top));
                rst.points.push_back(stPoint(box.right, box.bottom));
                rst.code = box.class_label;
                break;
        }
        rst_list.push_back(rst);
    }
    return rst_list;
}
```

**设计优势**:
- **算法封装**: 将不同的结果处理算法封装在独立的策略中
- **易于扩展**: 添加新的算法类型只需扩展 switch 语句
- **运行时选择**: 可以根据算法类型动态选择处理策略

## 并发模式

### 1. 生产者-消费者模式 (Producer-Consumer Pattern)

**应用场景**: 异步推理任务的处理流水线

**代码实现**:
```cpp
class AIRuntime : public AIRuntimeInterface {
private:
    // 三个队列实现多级生产者-消费者模式
    rigtorp::MPMCQueue<TaskInfoPtr> m_queueTask;        // 任务队列
    rigtorp::MPMCQueue<InferResultPtr> m_rstResult;     // 推理结果队列
    rigtorp::MPMCQueue<ModelResultPtr> m_queueResult;   // 最终结果队列
    
    // 三个工作线程
    std::thread mInferThread;       // 任务处理线程
    std::thread mRstListenerThread; // 结果监听线程
    std::thread mCallbackThread;    // 回调处理线程

public:
    // 生产者 - 提交推理任务
    eAIErrorCode CommitInferTask(TaskInfoPtr spTaskInfo) override {
        if (!m_queueTask.try_push(spTaskInfo)) {
            return E_QUEUUE_FULL;
        }
        m_condTask.notify_one();  // 通知消费者
        return E_OK;
    }
    
    // 消费者1 - 任务处理线程
    void TaskWorker() {
        while (true) {
            TaskInfoPtr spTaskInfo;
            bool found = m_queueTask.try_pop(spTaskInfo);  // 消费任务
            
            if (found && spTaskInfo->imageData.size() >= 1) {
                // 执行推理并生产结果
                InferResultPtr inferRst = std::make_shared<InferResult>();
                auto rst = m_modelMap[spTaskInfo->modelId]->commits(spTaskInfo->imageData);
                inferRst->promise_rst = rst;
                inferRst->taskInfo = spTaskInfo;
                
                while (!m_rstResult.try_push(inferRst)) {
                    // 队列满时等待
                }
                m_condInfer.notify_one();
            }
        }
    }
};
```

**设计优势**:
- **解耦处理**: 任务提交、推理执行、结果处理完全解耦
- **并行处理**: 多个阶段可以并行执行，提高吞吐量
- **缓冲机制**: 队列提供缓冲，平衡不同阶段的处理速度

### 2. Future/Promise 模式

**应用场景**: 异步推理结果的获取

**代码实现**:
```cpp
// 在推理接口中使用 Future 模式
namespace Algo {
    class Infer {
    public:
        // 返回 Future 对象，支持异步获取结果
        virtual std::shared_future<BoxArray> commit(const cv::Mat& image) = 0;
        virtual std::vector<std::shared_future<BoxArray>> commits(const std::vector<cv::Mat>& images) = 0;
    };
}

// 在用户代码中使用 Promise 进行同步
TaskInfoPtr task = std::make_shared<stTaskInfo>();
task->promiseResult = new std::promise<ModelResultPtr>();  // 创建 Promise

ai_obj->CommitInferTask(task);  // 提交异步任务

// 获取 Future 并等待结果
std::promise<ModelResultPtr>* promiseResult = static_cast<std::promise<ModelResultPtr>*>(task->promiseResult);
std::future<ModelResultPtr> futureRst = promiseResult->get_future();
ModelResultPtr rst = futureRst.get();  // 阻塞等待结果
```

**设计优势**:
- **异步非阻塞**: 提交任务后立即返回，不阻塞调用线程
- **结果同步**: 可以在需要时同步等待结果
- **异常处理**: 支持异步异常的传播和处理

## 架构模式

### 1. 分层架构 (Layered Architecture)

**应用场景**: 整个 AIRuntime 系统的分层设计

**架构层次**:
```mermaid
graph TB
    subgraph "表示层"
        A1[用户应用程序]
        A2[示例程序]
    end
    
    subgraph "接口层"
        B1[AIRuntimeInterface]
        B2[IModelResultListener]
        B3[数据结构定义]
    end
    
    subgraph "业务逻辑层"
        C1[AIRuntime 核心]
        C2[模型管理器]
        C3[任务调度器]
        C4[结果处理器]
    end
    
    subgraph "服务层"
        D1[推理引擎抽象]
        D2[算法适配器]
        D3[后端选择器]
    end
    
    subgraph "数据访问层"
        E1[TensorRT 引擎]
        E2[ONNX Runtime]
        E3[模型文件加载]
    end
    
    subgraph "基础设施层"
        F1[CUDA/cuDNN]
        F2[OpenCV]
        F3[第三方库]
    end
    
    A1 --> B1
    A2 --> B1
    B1 --> C1
    C1 --> D1
    D1 --> E1
    D1 --> E2
    E1 --> F1
    E2 --> F1
```

**层次职责**:
- **表示层**: 用户界面和应用程序入口
- **接口层**: 统一的 API 接口定义
- **业务逻辑层**: 核心业务逻辑和流程控制
- **服务层**: 推理服务的抽象和适配
- **数据访问层**: 具体的推理引擎实现
- **基础设施层**: 底层库和硬件抽象

### 2. 插件架构 (Plugin Architecture)

**应用场景**: 支持不同算法和推理后端的动态扩展

**设计理念**:
- 通过 `create_infer` 工厂函数实现插件式的算法加载
- 不同推理后端（TensorRT/ONNX Runtime）作为可插拔的组件
- 算法类型通过枚举和 switch 语句实现插件式选择

## 设计模式应用总结

### 模式协作关系

```mermaid
graph TB
    subgraph "用户层"
        A[用户应用] --> B[外观模式<br/>AIRuntimeInterface]
    end
    
    subgraph "控制层"
        B --> C[单例模式<br/>AIRuntime]
        C --> D[观察者模式<br/>结果通知]
    end
    
    subgraph "业务层"
        C --> E[策略模式<br/>结果处理]
        C --> F[命令模式<br/>任务封装]
    end
    
    subgraph "服务层"
        F --> G[工厂方法模式<br/>推理引擎创建]
        G --> H[适配器模式<br/>后端适配]
    end
    
    subgraph "并发层"
        C --> I[生产者-消费者模式<br/>任务队列]
        I --> J[Future/Promise模式<br/>异步结果]
    end
```

### 设计模式的价值体现

#### 1. **可维护性**
- **单一职责**: 每个类和模块都有明确的职责
- **开闭原则**: 对扩展开放，对修改关闭
- **依赖倒置**: 依赖抽象而不是具体实现

#### 2. **可扩展性**
- **工厂模式**: 轻松添加新的算法类型和推理后端
- **策略模式**: 灵活处理不同算法的结果格式
- **插件架构**: 支持动态加载新的功能模块

#### 3. **性能优化**
- **单例模式**: 避免重复创建昂贵的资源
- **生产者-消费者**: 实现高效的异步处理流水线
- **Future/Promise**: 支持非阻塞的异步操作

#### 4. **代码复用**
- **模板方法**: 复用通用的推理流程
- **外观模式**: 提供统一的接口，隐藏实现复杂性
- **适配器模式**: 复用现有的推理引擎实现

### 设计模式最佳实践

#### 1. **合理选择模式**
- 根据具体问题选择合适的设计模式
- 避免过度设计和模式滥用
- 考虑性能和复杂性的平衡

#### 2. **模式组合使用**
- 多个模式协同工作，解决复杂问题
- 保持模式之间的协调一致
- 避免模式冲突和重复

#### 3. **持续重构**
- 随着需求变化，适时引入新的设计模式
- 重构现有代码以符合设计模式
- 保持代码的清洁和可读性

## 学习建议

### 1. **理解设计原则**
在学习设计模式之前，先理解 SOLID 设计原则：
- **S**ingle Responsibility Principle (单一职责原则)
- **O**pen/Closed Principle (开闭原则)
- **L**iskov Substitution Principle (里氏替换原则)
- **I**nterface Segregation Principle (接口隔离原则)
- **D**ependency Inversion Principle (依赖倒置原则)

### 2. **从实际问题出发**
- 先遇到问题，再学习相应的设计模式
- 理解模式解决的具体问题和应用场景
- 通过实际项目加深对模式的理解

### 3. **循序渐进**
- 从简单的模式开始学习（如单例、工厂）
- 逐步学习复杂的模式（如观察者、策略）
- 最后学习架构级的模式（如分层、插件）

### 4. **实践应用**
- 在实际项目中应用学到的设计模式
- 分析开源项目中的设计模式使用
- 与团队成员讨论和分享设计模式经验

---

通过深入分析 AIRuntime 项目中的设计模式应用，我们可以看到优秀的软件架构是如何通过合理的设计模式组合来实现高内聚、低耦合、易扩展的系统。这些设计模式不仅提高了代码的可维护性和可扩展性，也为项目的长期发展奠定了坚实的基础。