# AIRuntime 项目目录结构与使用指南

## 📁 项目目录结构

### 根目录文件
```
G:\algo_ai - 副本\
├── xmake.lua                    # 主构建配置文件
├── README.md                    # 项目说明文档
├── 开发者快速参考.md             # API 速查手册
├── 技术架构文档.md               # 技术架构说明
├── 新人上手指南.md               # 新手入门指南
├── 算法使用示例.md               # 算法使用示例
└── 设计模式分析.md               # 设计模式分析
```

### 核心目录

#### 🔧 `3rdparty/` - 第三方依赖库
```
3rdparty/
├── concurrent_queue/            # 并发队列库
├── doctest/                     # 单元测试框架
├── fmt_x64-windows/             # 格式化输出库
├── nlohmann-json_x64-windows/   # JSON 处理库
├── opencv2/                     # OpenCV 计算机视觉库
└── spdlog_x64-windows/          # 日志库
```

#### 📦 `src/` - 源代码目录
```
src/
├── airuntime/                   # 核心运行时
│   ├── AIAlgoBase.cpp          # 算法基类实现
│   ├── AIRuntime.cpp           # 运行时核心实现
│   └── AIRuntimeInterface.cpp  # 运行时接口实现
├── ort/                        # ONNX Runtime 后端
│   ├── ort_app_classfication/  # ORT 分类算法
│   ├── ort_app_ocr/           # ORT OCR 算法
│   ├── ort_app_yolo/          # ORT YOLO 算法
│   ├── ort_app_yolo8/         # ORT YOLOv8 算法
│   └── ort_common/            # ORT 公共组件
└── trt/                        # TensorRT 后端
    ├── trt_app_anomalib/       # 异常检测算法
    ├── trt_app_classification/ # 图像分类算法
    ├── trt_app_deim/          # DEIM 算法
    ├── trt_app_msae/          # MSAE 算法
    ├── trt_app_ocr/           # OCR 文字识别算法
    ├── trt_app_segmentation/  # 图像分割算法
    ├── trt_app_yolo/          # YOLO 目标检测算法
    ├── trt_app_yolo8/         # YOLOv8 目标检测算法
    ├── trt_app_yolo8_obb/     # YOLOv8 旋转框检测算法
    ├── trt_app_yolo8seg/      # YOLOv8 实例分割算法
    ├── trt_common/            # TRT 公共组件
    └── trt_cuda/              # CUDA 加速组件
```

#### 📂 `include/` - 头文件目录
```
include/
├── private/                    # 内部头文件
│   ├── airuntime/             # 运行时内部接口
│   ├── ort/                   # ORT 内部接口
│   └── trt/                   # TRT 内部接口
└── public/                     # 公共头文件
    ├── AIRuntimeInterface.h   # 主要公共接口
    └── AIRuntimeDataStruct.h  # 数据结构定义
```

#### 🧪 `sample/` - 示例和测试
```
sample/
├── models/                     # 预训练模型
│   ├── yolov5s.engine         # YOLOv5 TensorRT 模型
│   ├── yolov5s.onnx          # YOLOv5 ONNX 模型
│   ├── yolov8s.engine        # YOLOv8 TensorRT 模型
│   └── yolov8s.onnx          # YOLOv8 ONNX 模型
├── test_data/                  # 测试数据
│   ├── test/                  # 输入测试图片 (512张)
│   └── output/                # 输出结果图片
├── trt_test/                   # TensorRT 测试程序
│   ├── example_trt_yolo.h     # YOLO 测试示例
│   ├── example_trt_ocr.h      # OCR 测试示例
│   ├── example_trt_anomalib.h # 异常检测测试示例
│   ├── example_trt_msae.h     # MSAE 测试示例
│   └── trt_test_main.cpp      # 测试主程序
├── ort_test/                   # ONNX Runtime 测试程序
└── dll_test/                   # DLL 接口测试程序
```

#### 🏗️ `build/` - 编译输出
```
build/windows/x64/release/
├── AIFramework.dll             # 主要运行时库
├── AIFramework.lib             # 静态链接库
├── trt_test.exe               # TensorRT 测试程序
└── *.pdb                      # 调试符号文件
```

#### 📦 `install/` - 安装输出
```
install/
├── AIFramework.dll             # 发布版本的 DLL
└── *.h                        # 公共头文件
```

## 🚀 各种检测算法使用指南

### 1. YOLO 目标检测 ✅ (已可用)

**支持的模型类型:**
- YOLOv5 (`yolov5s.engine`)
- YOLOv8 (`yolov8s.engine`)
- YOLOv8 OBB (旋转框检测)
- YOLOv8 Segmentation (实例分割)

**使用方法:**
```bash
# 1. 确保模型文件存在
ls sample/models/yolov5s.engine

# 2. 运行 YOLO 测试
xmake run trt_test

# 3. 查看结果
ls sample/test_data/output/
```

**自定义配置:**
编辑 `sample/trt_test/example_trt_yolo.h`:
```cpp
std::string modelPath = "G:/algo_ai - 副本/sample/models/yolov5s.engine";
float confidence_threshold = 0.25f;  // 置信度阈值
float nms_threshold = 0.25f;         // NMS 阈值
```

### 2. OCR 文字识别 🔧 (需要模型)

**所需模型:**
- 文字检测模型: `ocr_det.trtmodel`
- 文字识别模型: `ocr_rec.trtmodel`
- 字典文件: `ppocr_keys_v1.txt`

**使用方法:**
```bash
# 1. 准备 OCR 模型文件
# 将模型放入 sample/models/ 目录

# 2. 修改测试主程序
# 编辑 sample/trt_test/trt_test_main.cpp
# 取消注释: test_trt_ocr();
# 注释掉: test_yolo();

# 3. 重新编译运行
xmake
xmake run trt_test
```

### 3. 异常检测 (Anomalib) 🔧 (需要模型)

**所需模型:**
- 异常检测模型: `stfpm.trtmodel` 或其他 anomalib 模型

**使用方法:**
```bash
# 1. 准备异常检测模型
# 将模型放入 sample/models/ 目录

# 2. 修改路径配置
# 编辑 sample/trt_test/example_trt_anomalib.h
# 更新 modelPath 和 img_dir 路径

# 3. 修改测试主程序
# 编辑 sample/trt_test/trt_test_main.cpp
# 取消注释: test_anomalib();

# 4. 重新编译运行
xmake
xmake run trt_test
```

### 4. MSAE 算法 🔧 (需要模型)

**所需模型:**
- MSAE 模型: `msae_hgz_a.trtmodel`

**使用方法:**
```bash
# 1. 准备 MSAE 模型
# 将模型放入 sample/models/ 目录

# 2. 修改路径配置
# 编辑 sample/trt_test/example_trt_msae.h
# 更新 modelPath 和 img_dir 路径

# 3. 修改测试主程序
# 编辑 sample/trt_test/trt_test_main.cpp
# 取消注释: test_mase();

# 4. 重新编译运行
xmake
xmake run trt_test
```

### 5. 图像分类 🔧 (需要实现)

**状态:** 代码框架已存在，需要添加测试示例

### 6. 图像分割 🔧 (需要实现)

**状态:** 代码框架已存在，需要添加测试示例

## 🔄 切换不同算法的步骤

### 方法1: 修改测试主程序 (推荐)

1. **编辑测试主程序:**
   ```bash
   # 打开文件
   notepad sample/trt_test/trt_test_main.cpp
   ```

2. **选择要测试的算法:**
   ```cpp
   int main()
   {
       // test_yolo();        // YOLO 目标检测
       test_trt_ocr();        // OCR 文字识别
       // test_mase();        // MSAE 算法
       // test_anomalib();    // 异常检测
       return 0;
   }
   ```

3. **重新编译运行:**
   ```bash
   xmake
   xmake run trt_test
   ```

### 方法2: 创建新的测试程序

1. **复制现有测试:**
   ```bash
   cp sample/trt_test/trt_test_main.cpp sample/trt_test/my_test.cpp
   ```

2. **修改 xmake.lua 添加新目标**

3. **编译运行新程序**

## 📋 常用命令速查

```bash
# 编译项目
xmake

# 运行测试
xmake run trt_test

# 清理编译
xmake clean

# 重新配置
xmake f -c

# 查看帮助
xmake --help
```

## 🔍 故障排除

### 常见问题:

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认模型文件存在且完整

2. **CUDA 相关错误**
   - 确认 CUDA 12.4 已正确安装
   - 检查 GPU 驱动版本

3. **编译错误**
   - 检查第三方库路径配置
   - 确认 Visual Studio 2022 环境

4. **运行时错误**
   - 检查 DLL 依赖是否完整
   - 确认测试数据路径正确

---

**💡 提示:** 
- 首次使用建议从 YOLO 测试开始，因为模型已准备就绪
- 其他算法需要相应的模型文件才能正常运行
- 所有测试结果都会保存在 `sample/test_data/output/` 目录中
