{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/spdlog-x64-windows-1.11.0-67bb748f-8d50-4d78-9e15-8b03eb17323f", "name": "spdlog:x64-windows@1.11.0 ecfaa1a76fc9044de9ca36bcf8acc618b64be4172fcfc8de235fbc99e9167a4f", "creationInfo": {"creators": ["Tool: vcpkg-8a88d63f241d391772fbde69af9cab96c3c64c75"], "created": "2023-03-14T14:34:57Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "spdlog", "SPDXID": "SPDXRef-port", "versionInfo": "1.11.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/spdlog", "homepage": "https://github.com/gabime/spdlog", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Very fast, header only, C++ logging library", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "spdlog:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "ecfaa1a76fc9044de9ca36bcf8acc618b64be4172fcfc8de235fbc99e9167a4f", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-1", "name": "gabime/spdlog", "downloadLocation": "git+https://github.com/gabime/spdlog@v1.11.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "210f3135c7af3ec774ef9a5c77254ce172a44e2fa720bf590e1c9214782bf5c8140ff683403a85b585868bc308286fbdeb1c988e4ed1eb3c75975254ffe75412"}]}], "files": [{"fileName": "./fmt-header.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "365ece45e9df3bea580c106f7415a5dc13de22365c1671c1a7e5e5afe5461dc0"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "9b37fab66aca66c53722e019ee571c5063a5933d555e95f60d48470875dbb61a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "bdfe8aea499307fe3a026725a74ff78a276d0cf9fc75d6bdf63cbadc30bef1e6"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}