# AIRuntime 技术架构文档

## 系统架构概览

AIRuntime 是一个高性能的 AI 推理运行时框架，采用分层架构设计，支持多种推理后端和算法类型。

### 架构设计原则

1. **模块化设计**: 各组件职责清晰，低耦合高内聚
2. **可扩展性**: 支持新算法和推理后端的快速集成
3. **高性能**: 异步处理、内存优化、GPU 加速
4. **跨平台**: 支持 Windows 和 Linux 操作系统
5. **易用性**: 提供简洁统一的 C API 接口

## 详细架构设计

### 线程模型

```mermaid
graph TB
    subgraph "主线程"
        A[应用程序主线程]
    end
    
    subgraph "AIRuntime 工作线程"
        B[任务分发线程<br/>TaskWorker]
        C[推理执行线程<br/>ResultListenWorker]
        D[结果回调线程<br/>CallbackWorker]
    end
    
    subgraph "队列系统"
        E[任务队列<br/>m_queueTask]
        F[推理结果队列<br/>m_rstResult]
        G[回调队列<br/>m_queueResult]
    end
    
    subgraph "推理引擎"
        H[TensorRT Engine]
        I[ONNX Runtime]
    end
    
    A -->|CommitInferTask| E
    B -->|获取任务| E
    B -->|提交推理| F
    C -->|获取结果| F
    C -->|处理结果| G
    D -->|回调通知| A
    D -->|获取结果| G
    B -->|调用推理| H
    B -->|调用推理| I
```

### 内存管理架构

```mermaid
graph TB
    subgraph "CPU 内存"
        A[系统内存池]
        B[CPU 缓存<br/>CPUCachSize]
        C[固定内存<br/>Pinned Memory]
    end
    
    subgraph "GPU 内存"
        D[GPU 显存池]
        E[GPU 缓存<br/>GPUCachSize]
        F[推理工作空间<br/>workSpaceSize]
    end
    
    subgraph "数据流"
        G[输入图像数据]
        H[预处理结果]
        I[推理输入]
        J[推理输出]
        K[后处理结果]
    end
    
    G --> A
    A --> B
    B --> C
    C -->|DMA传输| D
    D --> E
    E --> F
    F --> I
    I -->|推理| J
    J --> K
    K -->|回传| A
```

## 核心组件详解

### 1. AIRuntime 核心类

**职责**: 整个推理系统的核心控制器

**关键成员**:
```cpp
class AIRuntime : public AIRuntimeInterface {
private:
    // 线程管理
    std::thread mInferThread;           // 推理线程
    std::thread mCallbackThread;        // 回调线程  
    std::thread mRstListenerThread;     // 结果监听线程
    
    // 队列系统
    moodycamel::BlockingConcurrentQueue<TaskInfoPtr> m_queueTask;
    moodycamel::BlockingConcurrentQueue<InferResultPtr> m_rstResult;
    moodycamel::BlockingConcurrentQueue<ModelResultPtr> m_queueResult;
    
    // 模型管理
    std::map<int, std::shared_ptr<Algo::Infer>> m_modelMap;
    std::map<int, stAIModelInfo> m_model_param;
    
    // 回调管理
    std::vector<IModelResultListener*> m_callbackList;
};
```

**工作流程**:
1. **初始化阶段**: 启动三个工作线程，初始化队列系统
2. **模型管理**: 根据配置创建和管理推理模型
3. **任务调度**: 接收推理任务，分发给推理引擎
4. **结果处理**: 收集推理结果，通过回调返回给用户

### 2. 推理引擎抽象层

**设计模式**: 策略模式 + 工厂模式

```cpp
namespace Algo {
    class Infer {
    public:
        virtual std::vector<std::shared_future<BoxArray>> 
            commits(const std::vector<cv::Mat>& images) = 0;
        virtual json infer_info() = 0;
        virtual void set_param(const json& param) = 0;
    };
}
```

**TensorRT 实现**:
- 基于 NVIDIA TensorRT 的高性能推理
- 支持 FP32/FP16/INT8 精度优化
- GPU 内存池管理和 CUDA 流优化

**ONNX Runtime 实现**:
- 跨平台推理支持
- CPU 和 GPU 执行提供程序
- 动态图优化

### 3. 算法适配层

每种算法都有独立的实现模块，遵循统一的接口规范：

```cpp
// 算法工厂函数示例
namespace yolo8 {
    std::shared_ptr<Algo::Infer> create_infer(
        const std::string& engine_file,
        int gpu_id,
        float confidence_threshold,
        float nms_threshold,
        int max_objects
    );
}
```

**支持的算法类型**:

| 算法类型 | 描述 | 输入 | 输出 |
|---------|------|------|------|
| CLASSIFY | 图像分类 | 单张图像 | 分类标签和置信度 |
| YOLOV5/YOLO8 | 目标检测 | 单张图像 | 边界框、类别、置信度 |
| OCR_DET | 文字检测 | 单张图像 | 文字区域四边形坐标 |
| OCR_REC | 文字识别 | 文字区域图像 | 识别文字和置信度 |
| YOLOV8_SEG | 实例分割 | 单张图像 | 边界框、掩码、类别 |
| ANOMALIB | 异常检测 | 单张图像 | 异常分数和热力图 |

## 数据流设计

### 推理数据流

```mermaid
flowchart TD
    A[输入图像] --> B[预处理]
    B --> C[数据格式转换]
    C --> D[GPU 内存拷贝]
    D --> E[模型推理]
    E --> F[结果解析]
    F --> G[后处理]
    G --> H[结果封装]
    H --> I[回调通知]
    
    subgraph "预处理阶段"
        B1[图像缩放]
        B2[归一化]
        B3[通道转换]
        B --> B1 --> B2 --> B3
    end
    
    subgraph "后处理阶段"
        G1[NMS 过滤]
        G2[坐标还原]
        G3[结果排序]
        G --> G1 --> G2 --> G3
    end
```

### 内存优化策略

1. **内存池管理**: 预分配固定大小的内存池，避免频繁分配
2. **固定内存**: 使用 CUDA 固定内存提高 CPU-GPU 传输效率
3. **零拷贝优化**: 尽可能减少不必要的内存拷贝操作
4. **批处理优化**: 支持批量推理，提高 GPU 利用率

## 性能优化设计

### 1. 异步处理架构

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant TaskQueue as 任务队列
    participant InferEngine as 推理引擎
    participant ResultQueue as 结果队列
    participant Callback as 回调系统
    
    App->>TaskQueue: 提交任务1
    App->>TaskQueue: 提交任务2
    App->>TaskQueue: 提交任务3
    
    par 并行处理
        TaskQueue->>InferEngine: 处理任务1
        TaskQueue->>InferEngine: 处理任务2
        TaskQueue->>InferEngine: 处理任务3
    end
    
    InferEngine->>ResultQueue: 结果1
    InferEngine->>ResultQueue: 结果2
    InferEngine->>ResultQueue: 结果3
    
    ResultQueue->>Callback: 通知结果1
    ResultQueue->>Callback: 通知结果2
    ResultQueue->>Callback: 通知结果3
    
    Callback->>App: 回调结果1
    Callback->>App: 回调结果2
    Callback->>App: 回调结果3
```

### 2. GPU 资源管理

**CUDA 流管理**:
- 使用多个 CUDA 流实现并行处理
- 内存传输和计算重叠执行
- 动态负载均衡

**内存管理**:
- GPU 内存池预分配
- 智能缓存策略
- 内存碎片整理

### 3. 批处理优化

**动态批处理**:
```cpp
class BatchProcessor {
private:
    std::vector<cv::Mat> batch_images_;
    std::vector<TaskInfoPtr> batch_tasks_;
    int max_batch_size_;
    std::chrono::milliseconds timeout_;
    
public:
    void AddTask(TaskInfoPtr task);
    std::vector<TaskInfoPtr> GetBatch();
    bool ShouldProcess();
};
```

## 错误处理和容错设计

### 错误分类

```cpp
enum eAIErrorCode {
    E_OK = 0,                    // 成功
    E_OUT_OF_MEMORY,            // 内存不足
    E_CREATE_MODEL_FAILED,      // 模型创建失败
    E_FILE_NOT_EXIST,           // 文件不存在
    E_QUEUUE_FULL,              // 队列满
    E_INVALID_PARAM,            // 参数无效
    E_GPU_ERROR,                // GPU 错误
    E_TIMEOUT,                  // 超时
};
```

### 容错机制

1. **资源监控**: 实时监控 GPU 内存和 CPU 使用率
2. **自动重试**: 对临时性错误进行自动重试
3. **降级处理**: GPU 不可用时自动切换到 CPU
4. **异常隔离**: 单个模型异常不影响其他模型

## 扩展性设计

### 1. 插件架构

```cpp
class AlgorithmPlugin {
public:
    virtual std::string GetName() = 0;
    virtual eAIAlgoType GetType() = 0;
    virtual std::shared_ptr<Algo::Infer> CreateInfer(const json& config) = 0;
};

class PluginManager {
public:
    void RegisterPlugin(std::shared_ptr<AlgorithmPlugin> plugin);
    std::shared_ptr<AlgorithmPlugin> GetPlugin(eAIAlgoType type);
};
```

### 2. 配置驱动

```json
{
    "runtime": {
        "preProcessThreadCnt": 8,
        "inferThreadCnt": 4,
        "usePinMemory": true,
        "workSpaceSize": 2048
    },
    "models": [
        {
            "modelId": 0,
            "modelName": "yolo8_detector",
            "modelPath": "models/yolo8.trtmodel",
            "modelBackend": "tensorrt",
            "algoType": "YOLO8",
            "inferParam": {
                "confidenceThreshold": 0.5,
                "nmsThreshold": 0.4,
                "maxObjectNums": 1000,
                "gpuId": 0
            }
        }
    ]
}
```

### 3. 热更新支持

```cpp
class ModelManager {
public:
    eAIErrorCode UpdateModel(int modelId, const stAIModelInfo& newModelInfo);
    eAIErrorCode ReloadModel(int modelId);
    eAIErrorCode SetModelParam(int modelId, const json& params);
};
```

## 监控和调试

### 1. 性能监控

```cpp
struct PerformanceMetrics {
    long long preprocess_time;      // 预处理时间
    long long inference_time;       // 推理时间
    long long postprocess_time;     // 后处理时间
    long long total_time;           // 总时间
    size_t gpu_memory_used;         // GPU 内存使用
    float gpu_utilization;          // GPU 利用率
};
```

### 2. 日志系统

```cpp
// 分级日志
LOG_INFO("模型 {} 创建成功", modelId);
LOG_INFOW("GPU 内存使用率过高: {}%", usage);
LOG_INFOE("模型加载失败: {}", error_msg);
LOG_INFOD("推理耗时: {}ms", inference_time);
```

### 3. 调试工具

- **内存泄漏检测**: 智能指针使用和资源自动释放
- **性能分析**: 内置计时器和性能统计
- **可视化调试**: 支持中间结果可视化

## 安全性考虑

### 1. 内存安全
- 使用智能指针管理资源
- 边界检查和缓冲区溢出防护
- 异常安全的 RAII 设计

### 2. 线程安全
- 无锁队列实现
- 原子操作和内存屏障
- 死锁检测和避免

### 3. 输入验证
- 模型文件完整性检查
- 输入参数范围验证
- 恶意输入防护

## 部署架构

### 1. 单机部署

```mermaid
graph TB
    subgraph "应用进程"
        A[主应用程序]
        B[AIRuntime DLL/SO]
    end
    
    subgraph "系统资源"
        C[GPU 0]
        D[GPU 1]
        E[CPU 核心]
        F[系统内存]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
```

### 2. 分布式部署

```mermaid
graph TB
    subgraph "负载均衡器"
        A[请求分发]
    end
    
    subgraph "推理节点1"
        B[AIRuntime 实例1]
        C[GPU 0]
    end
    
    subgraph "推理节点2"
        D[AIRuntime 实例2]
        E[GPU 1]
    end
    
    subgraph "推理节点3"
        F[AIRuntime 实例3]
        G[GPU 2]
    end
    
    A --> B
    A --> D
    A --> F
    B --> C
    D --> E
    F --> G
```

## 版本演进规划

### 当前版本 (v0.0.1)
- 基础推理框架
- TensorRT 和 ONNX Runtime 支持
- 主要算法实现

### 下一版本规划
- **v0.1.0**: 
  - 分布式推理支持
  - 模型热更新
  - 更多算法支持
  
- **v0.2.0**:
  - 云原生部署
  - 自动扩缩容
  - 监控告警系统

- **v1.0.0**:
  - 生产级稳定性
  - 完整的运维工具链
  - 企业级安全特性

---

本文档详细描述了 AIRuntime 的技术架构设计，为开发者提供了深入理解系统内部机制的参考。随着项目的发展，本文档将持续更新和完善。