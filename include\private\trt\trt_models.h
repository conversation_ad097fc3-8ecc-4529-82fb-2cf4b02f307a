
#ifndef __TRT_MODELS_H__
#define __TRT_MODELS_H__


#include "./trt_app_classification/classification.hpp"
#include "./trt_app_ocr/det.hpp"
#include "./trt_app_ocr/cls.hpp"
#include "./trt_app_ocr/rec.hpp"
#include "./trt_app_segmentation/unet.hpp"
#include "./trt_app_yolo/yolo.hpp"
#include "./trt_app_yolo8/yolo8.hpp"
#include "./trt_app_msae/msae.hpp"
#include "./trt_app_anomalib/anomalib.hpp"
#include "./trt_app_yolo8_obb/yolo8_obb.hpp"
#include "./trt_app_yolo8seg/yolo8_seg.hpp"
#include "./trt_app_deim/deim.hpp"
#endif // !__TRT_MODELS_H__


