#include <iostream>
#include <string>
#include <filesystem>
#include <exception>
// Conditional include to avoid immediate crash if TensorRT is not available
// #include "../include/public/AIRuntimeInterface.h"

// Forward declaration to avoid linking issues when TensorRT is not available
extern "C" __declspec(dllimport) bool build_model(int model_type, int max_batch_size, const char* onnx_path, const char* model_save_path, const size_t max_work_space_size);

void print_usage(const char* program_name) {
    std::cout << "ONNX to TensorRT Engine Converter\n";
    std::cout << "Usage: " << program_name << " <onnx_path> <engine_path> [options]\n\n";
    std::cout << "Arguments:\n";
    std::cout << "  onnx_path        Path to input ONNX model file\n";
    std::cout << "  engine_path      Path to output TensorRT engine file\n\n";
    std::cout << "Options:\n";
    std::cout << "  --fp16           Use FP16 precision (default: FP32)\n";
    std::cout << "  --batch <size>   Maximum batch size (default: 1)\n";
    std::cout << "  --workspace <MB> Workspace size in MB (default: 1024)\n";
    std::cout << "  --help, -h       Show this help message\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << program_name << " model.onnx model.engine\n";
    std::cout << "  " << program_name << " model.onnx model.engine --fp16 --batch 8 --workspace 2048\n";
}

int main(int argc, char** argv) {
    try {
        if (argc < 3) {
            print_usage(argv[0]);
            return -1;
        }

        // Check for help flag
        for (int i = 1; i < argc; i++) {
            std::string arg = argv[i];
            if (arg == "--help" || arg == "-h") {
                print_usage(argv[0]);
                return 0;
            }
        }

    std::string onnx_path = argv[1];
    std::string engine_path = argv[2];
    
    // Default parameters
    int model_type = 0;        // 0: FP32, 1: FP16
    int max_batch_size = 1;
    size_t workspace_size = 1024 * 1024 * 1024; // 1GB default

    // Parse optional arguments
    for (int i = 3; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "--fp16") {
            model_type = 1;
        }
        else if (arg == "--batch" && i + 1 < argc) {
            max_batch_size = std::atoi(argv[++i]);
            if (max_batch_size <= 0) {
                std::cerr << "Error: Invalid batch size: " << argv[i] << std::endl;
                return -1;
            }
        }
        else if (arg == "--workspace" && i + 1 < argc) {
            int workspace_mb = std::atoi(argv[++i]);
            if (workspace_mb <= 0) {
                std::cerr << "Error: Invalid workspace size: " << argv[i] << std::endl;
                return -1;
            }
            workspace_size = (size_t)workspace_mb * 1024 * 1024;
        }
        else {
            std::cerr << "Error: Unknown argument: " << arg << std::endl;
            print_usage(argv[0]);
            return -1;
        }
    }

    // Check if input file exists
    if (!std::filesystem::exists(onnx_path)) {
        std::cerr << "Error: ONNX file not found: " << onnx_path << std::endl;
        return -1;
    }

    // Create output directory if it doesn't exist
    std::filesystem::path engine_file_path(engine_path);
    if (engine_file_path.has_parent_path()) {
        std::filesystem::create_directories(engine_file_path.parent_path());
    }

    // Print conversion parameters
    std::cout << "=== ONNX to TensorRT Engine Conversion ===" << std::endl;
    std::cout << "Input ONNX:     " << onnx_path << std::endl;
    std::cout << "Output Engine:  " << engine_path << std::endl;
    std::cout << "Precision:      " << (model_type == 1 ? "FP16" : "FP32") << std::endl;
    std::cout << "Max Batch Size: " << max_batch_size << std::endl;
    std::cout << "Workspace Size: " << workspace_size / (1024 * 1024) << " MB" << std::endl;
    std::cout << "===========================================" << std::endl;

    // Start conversion
    std::cout << "Starting conversion..." << std::endl;
    
    bool success = build_model(model_type, max_batch_size, onnx_path.c_str(), engine_path.c_str(), workspace_size);
    
    if (success) {
        std::cout << "✓ Conversion completed successfully!" << std::endl;
        std::cout << "Engine saved to: " << engine_path << std::endl;
        return 0;
    } else {
        std::cerr << "✗ Conversion failed!" << std::endl;
        return -1;
    }
    
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unknown error occurred!" << std::endl;
        return -1;
    }
}
