#ifndef CPLUGIN_H
#define CPLUGIN_H

#include "../HQ_Share/CPluginCore.h"

class CPlugin : public CPluginCore
{
    /*!<	重新定义base类型	*/
    typedef CPluginCore base;

public:
    CPlugin(const char* pstrKey, IPlugin* pIHost);
    virtual ~CPlugin();


protected:
    virtual int OnInitialize();
    virtual int OnUninitialize();
    virtual int OnStartup();
    virtual int OnShutdown();

};


#endif // CPLUGIN_H
