cmake 3.25.1
features core
portfile.cmake 20e24863e86739ae6c15132133a78948ec819a27fe1acfa09768635e253824b4
ports.cmake 47a3510fcec56fae26f4fb082afd972a70a04e26efa73e2de69123139500f02d
post_build_checks 2
powershell 7.2.8
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-2b9f5a18de013332e7068fafe850651fd91434ba2a08aae552b8565a2236f81b-83d9dacea1b6b65a04acd5f32bc36b9e3c637eb0
usage ec6460d4688d7908d8adc698700f77639013641f1ab27066e9324c9ca11276c3
vcpkg-cmake 3ca4909ccff51ff010c1af28213ddddee8a83fbff46f6fd3ef3ee14f4ec78c49
vcpkg-cmake-config 9eac6a999bcac02dbba06e3951ca85857bf7e9d259f4d1e11cd891c1ff8cffbf
vcpkg.json f94cff0a5dd2cbfe3c131f86f210fa8861ccd3daa303d6b9f7b0514a8a4bc218
vcpkg_check_features 3cdc889b7e6965ad5ddab803060aed2019c0177b6f314c7a5a947c01efa4db60
vcpkg_fixup_pkgconfig e61be31d6539cc056e70b4f6c124e8da1de30e501a438e8cf358379ce744680d
vcpkg_from_git 8f27bff0d01c6d15a3e691758df52bfbb0b1b929da45c4ebba02ef76b54b1881
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string d43c8699ce27e25d47367c970d1c546f6bc36b6df8fb0be0c3986eb5830bd4f1
