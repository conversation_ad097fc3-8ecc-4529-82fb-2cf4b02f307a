# 🔄 AIRuntime 算法切换操作手册

## 📋 当前可用算法状态

| 算法类型 | 状态 | 模型文件 | 测试函数 | 说明 |
|---------|------|----------|----------|------|
| **YOLO 目标检测** | ✅ 可用 | `yolov5s.engine` | `test_yolo()` | 已测试通过 |
| **OCR 文字识别** | 🔧 需要模型 | `ocr_det.trtmodel`, `ocr_rec.trtmodel` | `test_trt_ocr()` | 代码就绪，缺模型 |
| **异常检测** | 🔧 需要模型 | `stfpm.trtmodel` | `test_anomalib()` | 代码就绪，缺模型 |
| **MSAE 算法** | 🔧 需要模型 | `msae_hgz_a.trtmodel` | `test_mase()` | 代码就绪，缺模型 |
| **图像分类** | 🚧 开发中 | - | `test_trt_classification()` | 需要实现 |
| **图像分割** | 🚧 开发中 | - | - | 需要实现 |

## 🚀 快速切换算法

### 步骤1: 修改测试主程序

打开测试主程序文件：
```
sample/trt_test/trt_test_main.cpp
```

当前内容：
```cpp
int main()
{
    test_yolo();           // ✅ 当前启用
    // test_trt_ocr();     // ❌ 当前禁用
    // test_mase();        // ❌ 当前禁用
    // test_anomalib();    // ❌ 当前禁用
    return 0;
}
```

### 步骤2: 选择要测试的算法

#### 🎯 切换到 OCR 文字识别
```cpp
int main()
{
    // test_yolo();        // 注释掉 YOLO
    test_trt_ocr();        // 启用 OCR
    // test_mase();
    // test_anomalib();
    return 0;
}
```

#### 🔍 切换到异常检测
```cpp
int main()
{
    // test_yolo();
    // test_trt_ocr();
    // test_mase();
    test_anomalib();       // 启用异常检测
    return 0;
}
```

#### 🎯 切换到 MSAE 算法
```cpp
int main()
{
    // test_yolo();
    // test_trt_ocr();
    test_mase();           // 启用 MSAE
    // test_anomalib();
    return 0;
}
```

#### 🔄 同时运行多个算法
```cpp
int main()
{
    test_yolo();           // 运行 YOLO
    test_trt_ocr();        // 然后运行 OCR
    test_anomalib();       // 最后运行异常检测
    return 0;
}
```

### 步骤3: 重新编译和运行

```bash
# 重新编译
xmake

# 运行测试
xmake run trt_test

# 查看结果
ls sample/test_data/output/
```

## 📁 各算法的路径配置

### 🎯 YOLO 目标检测 (已配置)

文件: `sample/trt_test/example_trt_yolo.h`
```cpp
std::string testDir   = R"(G:\algo_ai - 副本\sample\models\)";
std::string modelPath = testDir + "yolov5s.engine";
std::string img_dir   = R"(G:\algo_ai - 副本\sample\test_data\test\)";
std::string save_dir  = R"(G:\algo_ai - 副本\sample\test_data\output\)";
```

### 🔤 OCR 文字识别 (需要配置)

文件: `sample/trt_test/example_trt_ocr.h`

**当前路径 (需要修改):**
```cpp
// 第 140 行附近 - 检测模型
std::string det_model_path = R"(E:\git\algo_ai\sample\models\ocr_det.trtmodel)";

// 第 170 行附近 - 识别模型  
std::string rec_model_path = R"(E:\git\algo_ai\sample\models\ocr_rec.trtmodel)";

// 第 180 行附近 - 测试图片
std::string img_dir = R"(E:\git\algo_ai\sample\test_data\test\)";
```

**需要修改为:**
```cpp
std::string det_model_path = R"(G:\algo_ai - 副本\sample\models\ocr_det.trtmodel)";
std::string rec_model_path = R"(G:\algo_ai - 副本\sample\models\ocr_rec.trtmodel)";
std::string img_dir = R"(G:\algo_ai - 副本\sample\test_data\test\)";
```

### 🔍 异常检测 (需要配置)

文件: `sample/trt_test/example_trt_anomalib.h`

**当前路径 (需要修改):**
```cpp
std::string testDir   = R"(E:\model\fastflow\)";
std::string modelPath = testDir + "stfpm.trtmodel";
std::string img_dir   = R"(E:\model\fastflow\0301\*.jpg)";
```

**需要修改为:**
```cpp
std::string testDir   = R"(G:\algo_ai - 副本\sample\models\)";
std::string modelPath = testDir + "stfpm.trtmodel";
std::string img_dir   = R"(G:\algo_ai - 副本\sample\test_data\test\*.jpg)";
```

### 🎯 MSAE 算法 (需要配置)

文件: `sample/trt_test/example_trt_msae.h`

**当前路径 (需要修改):**
```cpp
std::string testDir   = R"(E:\project\ai_inference\Model\)";
std::string modelPath = testDir + "msae_hgz_a.trtmodel";
std::string img_dir   = R"(E:\project\ai_inference\test_img\*.jpg)";
```

**需要修改为:**
```cpp
std::string testDir   = R"(G:\algo_ai - 副本\sample\models\)";
std::string modelPath = testDir + "msae_hgz_a.trtmodel";
std::string img_dir   = R"(G:\algo_ai - 副本\sample\test_data\test\*.jpg)";
```

## 🔧 准备模型文件

### OCR 模型文件
需要将以下文件放入 `sample/models/` 目录：
```
sample/models/
├── ocr_det.trtmodel      # 文字检测模型
├── ocr_rec.trtmodel      # 文字识别模型
└── ppocr_keys_v1.txt     # 字典文件
```

### 异常检测模型文件
```
sample/models/
└── stfpm.trtmodel        # 异常检测模型
```

### MSAE 模型文件
```
sample/models/
└── msae_hgz_a.trtmodel   # MSAE 算法模型
```

## 📝 完整操作示例

### 示例1: 从 YOLO 切换到 OCR

1. **准备 OCR 模型文件**
   ```bash
   # 将 OCR 模型文件复制到 sample/models/ 目录
   cp /path/to/ocr_det.trtmodel sample/models/
   cp /path/to/ocr_rec.trtmodel sample/models/
   cp /path/to/ppocr_keys_v1.txt sample/models/
   ```

2. **修改 OCR 路径配置**
   ```bash
   # 编辑 OCR 测试文件
   notepad sample/trt_test/example_trt_ocr.h
   # 将所有 E:\ 路径改为 G:\algo_ai - 副本\
   ```

3. **修改测试主程序**
   ```bash
   # 编辑主程序
   notepad sample/trt_test/trt_test_main.cpp
   # 注释 test_yolo(); 取消注释 test_trt_ocr();
   ```

4. **编译运行**
   ```bash
   xmake
   xmake run trt_test
   ```

### 示例2: 运行多个算法

1. **修改主程序同时启用多个算法**
   ```cpp
   int main()
   {
       LOG_INFO("开始运行 YOLO 检测...");
       test_yolo();
       
       LOG_INFO("开始运行 OCR 识别...");
       test_trt_ocr();
       
       LOG_INFO("开始运行异常检测...");
       test_anomalib();
       
       return 0;
   }
   ```

2. **编译运行**
   ```bash
   xmake
   xmake run trt_test
   ```

## ⚠️ 注意事项

1. **模型文件格式**
   - TensorRT 模型: `.trtmodel` 或 `.engine`
   - ONNX 模型: `.onnx`
   - 确保模型与当前 TensorRT 版本兼容

2. **路径配置**
   - 所有路径必须使用绝对路径
   - Windows 路径使用 `R"()"` 原始字符串
   - 路径分隔符使用 `\` 或 `/`

3. **内存管理**
   - 大模型可能需要大量 GPU 内存
   - 同时运行多个算法时注意内存使用

4. **性能考虑**
   - 首次运行可能较慢（模型加载）
   - 后续推理会更快

## 🔍 故障排除

### 模型加载失败
```
错误: Can not load model : /path/to/model
解决: 检查模型文件是否存在，路径是否正确
```

### 编译错误
```
错误: 找不到头文件
解决: 检查 include 路径配置
```

### 运行时错误
```
错误: GPU 内存不足
解决: 减少批处理大小或使用更小的模型
```

---

**💡 提示:**
- 建议先从 YOLO 开始测试，确保环境正常
- 逐个添加其他算法，便于排查问题
- 保留原始配置文件的备份
