# AIRuntime 算法使用示例

本文档提供了 AIRuntime 支持的各种算法的详细使用示例，包括完整的代码实现、参数配置和结果处理。

## 目录

- [图像分类算法](#图像分类算法)
- [目标检测算法](#目标检测算法)
- [OCR 文字识别](#ocr-文字识别)
- [图像分割算法](#图像分割算法)
- [异常检测算法](#异常检测算法)
- [旋转目标检测](#旋转目标检测)
- [批处理推理](#批处理推理)
- [性能优化示例](#性能优化示例)

## 图像分类算法

### 基础分类示例

```cpp
#include "AIRuntimeInterface.h"
#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>

class ImageClassifier {
private:
    AIRuntimeInterface* runtime_;
    int model_id_;
    std::vector<std::string> class_names_;
    
public:
    ImageClassifier() : runtime_(GetAIRuntime()), model_id_(0) {
        // 初始化类别名称
        class_names_ = {
            "飞机", "汽车", "鸟", "猫", "鹿", 
            "狗", "青蛙", "马", "船", "卡车"
        };
    }
    
    bool Initialize(const std::string& model_path) {
        // 初始化运行时
        stAIConfigInfo config;
        config.preProcessThreadCnt = 4;
        config.inferThreadCnt = 2;
        config.usePinMemory = true;
        
        if (runtime_->InitRuntime(config) != E_OK) {
            std::cerr << "运行时初始化失败" << std::endl;
            return false;
        }
        
        // 创建分类模型
        stAIModelInfo modelInfo;
        modelInfo.modelId = model_id_;
        modelInfo.modelName = "image_classifier";
        modelInfo.modelPath = model_path;
        modelInfo.modelBackend = "tensorrt";
        modelInfo.algoType = CLASSIFY;
        
        // 设置推理参数
        modelInfo.inferParam.confidenceThreshold = 0.1f;  // 分类通常设置较低阈值
        modelInfo.inferParam.gpuId = 0;
        modelInfo.inferParam.maxBatchSize = 8;
        
        if (runtime_->CreateModle(modelInfo) != E_OK) {
            std::cerr << "模型创建失败" << std::endl;
            return false;
        }
        
        std::cout << "图像分类器初始化成功" << std::endl;
        return true;
    }
    
    struct ClassificationResult {
        int class_id;
        std::string class_name;
        float confidence;
    };
    
    std::vector<ClassificationResult> Classify(const cv::Mat& image) {
        std::vector<ClassificationResult> results;
        
        if (image.empty()) {
            std::cerr << "输入图像为空" << std::endl;
            return results;
        }
        
        // 创建推理任务
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {image};
        
        // 执行推理
        ModelResultPtr result = runtime_->RunInferTask(task);
        
        if (!result || result->itemList.empty()) {
            std::cerr << "推理失败或无结果" << std::endl;
            return results;
        }
        
        // 处理结果
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                ClassificationResult cls_result;
                cls_result.class_id = item.code;
                cls_result.confidence = item.confidence;
                
                if (cls_result.class_id >= 0 && cls_result.class_id < class_names_.size()) {
                    cls_result.class_name = class_names_[cls_result.class_id];
                } else {
                    cls_result.class_name = "未知类别";
                }
                
                results.push_back(cls_result);
            }
        }
        
        // 按置信度排序
        std::sort(results.begin(), results.end(), 
                  [](const ClassificationResult& a, const ClassificationResult& b) {
                      return a.confidence > b.confidence;
                  });
        
        return results;
    }
    
    void PrintResults(const std::vector<ClassificationResult>& results, int top_k = 5) {
        std::cout << "=== 分类结果 ===" << std::endl;
        int count = std::min(top_k, static_cast<int>(results.size()));
        
        for (int i = 0; i < count; ++i) {
            const auto& result = results[i];
            std::cout << "Top " << (i + 1) << ": " 
                      << result.class_name << " (ID: " << result.class_id 
                      << ", 置信度: " << std::fixed << std::setprecision(4) 
                      << result.confidence << ")" << std::endl;
        }
    }
    
    ~ImageClassifier() {
        if (runtime_) {
            runtime_->DestroyModle(model_id_);
            runtime_->DestoryRuntime();
        }
    }
};
```

## 目标检测算法

### YOLOv8 检测示例

```cpp
class YOLOv8Detector {
private:
    AIRuntimeInterface* runtime_;
    int model_id_;
    std::vector<std::string> class_names_;
    
public:
    YOLOv8Detector() : runtime_(GetAIRuntime()), model_id_(1) {
        // COCO 数据集类别名称
        class_names_ = {
            "人", "自行车", "汽车", "摩托车", "飞机", "公交车", "火车", "卡车",
            "船", "交通灯", "消防栓", "停车标志", "停车计时器", "长椅", "鸟", "猫",
            "狗", "马", "羊", "牛", "大象", "熊", "斑马", "长颈鹿", "背包"
            // ... 更多类别
        };
    }
    
    bool Initialize(const std::string& model_path) {
        stAIConfigInfo config;
        config.preProcessThreadCnt = 8;
        config.inferThreadCnt = 4;
        config.usePinMemory = true;
        config.workSpaceSize = 4096;  // 4GB 工作空间
        
        if (runtime_->InitRuntime(config) != E_OK) {
            return false;
        }
        
        stAIModelInfo modelInfo;
        modelInfo.modelId = model_id_;
        modelInfo.modelName = "yolov8_detector";
        modelInfo.modelPath = model_path;
        modelInfo.modelBackend = "tensorrt";
        modelInfo.algoType = YOLO8;
        
        // 设置检测参数
        modelInfo.inferParam.confidenceThreshold = 0.5f;   // 置信度阈值
        modelInfo.inferParam.nmsThreshold = 0.4f;          // NMS 阈值
        modelInfo.inferParam.maxObjectNums = 1000;         // 最大检测目标数
        modelInfo.inferParam.gpuId = 0;
        modelInfo.inferParam.maxBatchSize = 4;
        
        return runtime_->CreateModle(modelInfo) == E_OK;
    }
    
    struct DetectionResult {
        int class_id;
        std::string class_name;
        float confidence;
        cv::Rect bbox;
        cv::Point2f center;
    };
    
    std::vector<DetectionResult> Detect(const cv::Mat& image) {
        std::vector<DetectionResult> detections;
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {image};
        
        ModelResultPtr result = runtime_->RunInferTask(task);
        
        if (!result || result->itemList.empty()) {
            return detections;
        }
        
        // 处理检测结果
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                if (item.points.size() >= 2) {
                    DetectionResult detection;
                    detection.class_id = item.code;
                    detection.confidence = item.confidence;
                    
                    // 获取边界框
                    float x1 = item.points[0].x;
                    float y1 = item.points[0].y;
                    float x2 = item.points[1].x;
                    float y2 = item.points[1].y;
                    
                    detection.bbox = cv::Rect(
                        static_cast<int>(x1), static_cast<int>(y1),
                        static_cast<int>(x2 - x1), static_cast<int>(y2 - y1)
                    );
                    
                    detection.center = cv::Point2f((x1 + x2) / 2, (y1 + y2) / 2);
                    
                    // 获取类别名称
                    if (detection.class_id >= 0 && detection.class_id < class_names_.size()) {
                        detection.class_name = class_names_[detection.class_id];
                    } else {
                        detection.class_name = "未知";
                    }
                    
                    detections.push_back(detection);
                }
            }
        }
        
        return detections;
    }
    
    cv::Mat DrawDetections(const cv::Mat& image, 
                          const std::vector<DetectionResult>& detections) {
        cv::Mat result_image = image.clone();
        
        for (const auto& detection : detections) {
            // 绘制边界框
            cv::Scalar color(0, 255, 0);  // 绿色
            cv::rectangle(result_image, detection.bbox, color, 2);
            
            // 绘制标签
            std::string label = detection.class_name + " " + 
                               std::to_string(static_cast<int>(detection.confidence * 100)) + "%";
            
            int baseline = 0;
            cv::Size text_size = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseline);
            
            cv::Point label_pos(detection.bbox.x, detection.bbox.y - 10);
            cv::rectangle(result_image, 
                         cv::Point(label_pos.x, label_pos.y - text_size.height - baseline),
                         cv::Point(label_pos.x + text_size.width, label_pos.y + baseline),
                         color, -1);
            
            cv::putText(result_image, label, label_pos, 
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0), 1);
        }
        
        return result_image;
    }
};
```

## OCR 文字识别

### 完整 OCR 流程示例

```cpp
class OCRProcessor {
private:
    AIRuntimeInterface* runtime_;
    int det_model_id_;  // 文字检测模型ID
    int rec_model_id_;  // 文字识别模型ID
    
public:
    OCRProcessor() : runtime_(GetAIRuntime()), 
                    det_model_id_(10), rec_model_id_(11) {}
    
    bool Initialize(const std::string& det_model_path,
                   const std::string& rec_model_path,
                   const std::string& dict_path) {
        
        stAIConfigInfo config;
        config.preProcessThreadCnt = 6;
        config.inferThreadCnt = 3;
        config.usePinMemory = true;
        
        if (runtime_->InitRuntime(config) != E_OK) {
            return false;
        }
        
        // 创建文字检测模型
        stAIModelInfo detModel;
        detModel.modelId = det_model_id_;
        detModel.modelName = "ocr_detector";
        detModel.modelPath = det_model_path;
        detModel.modelBackend = "tensorrt";
        detModel.algoType = OCR_DET;
        
        // 设置检测参数
        detModel.inferParam.det_db_box_thresh = 0.6f;      // DB 检测阈值
        detModel.inferParam.det_db_unclip_ratio = 1.5f;    // 展开比例
        detModel.inferParam.max_side_len = 960;            // 最大边长
        detModel.inferParam.enableDetMat = true;           // 启用检测矩阵输出
        
        if (runtime_->CreateModle(detModel) != E_OK) {
            std::cerr << "文字检测模型创建失败" << std::endl;
            return false;
        }
        
        // 创建文字识别模型
        stAIModelInfo recModel;
        recModel.modelId = rec_model_id_;
        recModel.modelName = "ocr_recognizer";
        recModel.modelPath = rec_model_path;
        recModel.modelBackend = "tensorrt";
        recModel.algoType = OCR_REC;
        recModel.modleLabelPath = dict_path;               // 字典文件路径
        recModel.inferParam.confidenceThreshold = 0.5f;
        
        if (runtime_->CreateModle(recModel) != E_OK) {
            std::cerr << "文字识别模型创建失败" << std::endl;
            return false;
        }
        
        std::cout << "OCR 处理器初始化成功" << std::endl;
        return true;
    }
    
    struct OCRResult {
        std::vector<cv::Point2f> text_region;  // 文字区域四边形
        std::string text;                      // 识别的文字
        float confidence;                      // 置信度
        cv::Mat cropped_image;                 // 裁剪的文字图像
    };
    
    std::vector<OCRResult> ProcessImage(const cv::Mat& image) {
        std::vector<OCRResult> ocr_results;
        
        if (image.empty()) {
            return ocr_results;
        }
        
        // 第一步：文字检测
        auto detection_results = DetectText(image);
        if (detection_results.empty()) {
            std::cout << "未检测到文字区域" << std::endl;
            return ocr_results;
        }
        
        std::cout << "检测到 " << detection_results.size() << " 个文字区域" << std::endl;
        
        // 第二步：文字识别
        for (const auto& det_result : detection_results) {
            if (!det_result.cropped_image.empty()) {
                std::string recognized_text = RecognizeText(det_result.cropped_image);
                
                if (!recognized_text.empty()) {
                    OCRResult ocr_result;
                    ocr_result.text_region = det_result.text_region;
                    ocr_result.text = recognized_text;
                    ocr_result.confidence = det_result.confidence;
                    ocr_result.cropped_image = det_result.cropped_image.clone();
                    
                    ocr_results.push_back(ocr_result);
                }
            }
        }
        
        return ocr_results;
    }
    
private:
    struct DetectionResult {
        std::vector<cv::Point2f> text_region;
        float confidence;
        cv::Mat cropped_image;
    };
    
    std::vector<DetectionResult> DetectText(const cv::Mat& image) {
        std::vector<DetectionResult> results;
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = det_model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {image};
        
        ModelResultPtr result = runtime_->RunInferTask(task);
        
        if (!result || result->itemList.empty()) {
            return results;
        }
        
        // 处理检测结果
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                if (item.points.size() >= 4) {
                    DetectionResult det_result;
                    det_result.confidence = item.confidence;
                    
                    // 获取四边形顶点
                    for (const auto& point : item.points) {
                        det_result.text_region.emplace_back(point.x, point.y);
                    }
                    
                    // 获取裁剪的文字图像
                    if (!item.ocr_det.empty()) {
                        det_result.cropped_image = item.ocr_det.clone();
                    }
                    
                    results.push_back(det_result);
                }
            }
        }
        
        return results;
    }
    
    std::string RecognizeText(const cv::Mat& text_image) {
        if (text_image.empty()) {
            return "";
        }
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = rec_model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {text_image};
        
        ModelResultPtr result = runtime_->RunInferTask(task);
        
        if (!result || result->itemList.empty()) {
            return "";
        }
        
        // 处理识别结果
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                if (!item.ocr_str.empty()) {
                    return item.ocr_str;
                }
            }
        }
        
        return "";
    }
};
```

## 图像分割算法

### YOLOv8 实例分割示例

```cpp
class YOLOv8Segmentation {
private:
    AIRuntimeInterface* runtime_;
    int model_id_;
    std::vector<std::string> class_names_;
    
public:
    YOLOv8Segmentation() : runtime_(GetAIRuntime()), model_id_(20) {
        class_names_ = {"人", "汽车", "自行车", "摩托车"};  // 简化的类别
    }
    
    bool Initialize(const std::string& model_path) {
        stAIConfigInfo config;
        config.preProcessThreadCnt = 6;
        config.inferThreadCnt = 2;
        config.usePinMemory = true;
        config.workSpaceSize = 6144;  // 6GB 工作空间
        
        if (runtime_->InitRuntime(config) != E_OK) {
            return false;
        }
        
        stAIModelInfo modelInfo;
        modelInfo.modelId = model_id_;
        modelInfo.modelName = "yolov8_segmentation";
        modelInfo.modelPath = model_path;
        modelInfo.modelBackend = "tensorrt";
        modelInfo.algoType = YOLOV8_SEG;
        
        // 设置分割参数
        modelInfo.inferParam.confidenceThreshold = 0.5f;   // 置信度阈值
        modelInfo.inferParam.nmsThreshold = 0.4f;          // NMS 阈值
        modelInfo.inferParam.segThreshold = 0.5f;          // 分割阈值
        modelInfo.inferParam.maxObjectNums = 100;          // 最大目标数
        modelInfo.inferParam.maxAreaCont = true;           // 使用最大面积轮廓
        modelInfo.inferParam.gpuId = 0;
        
        return runtime_->CreateModle(modelInfo) == E_OK;
    }
    
    struct SegmentationResult {
        int class_id;
        std::string class_name;
        float confidence;
        cv::Rect bbox;
        std::vector<std::vector<cv::Point>> contours;  // 分割轮廓
        cv::Mat mask;                                  // 分割掩码
    };
    
    std::vector<SegmentationResult> Segment(const cv::Mat& image) {
        std::vector<SegmentationResult> results;
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {image};
        
        ModelResultPtr result = runtime_->RunInferTask(task);
        
        if (!result || result->itemList.empty()) {
            return results;
        }
        
        // 处理分割结果
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                SegmentationResult seg_result;
                seg_result.class_id = item.code;
                seg_result.confidence = item.confidence;
                
                // 获取类别名称
                if (seg_result.class_id >= 0 && seg_result.class_id < class_names_.size()) {
                    seg_result.class_name = class_names_[seg_result.class_id];
                } else {
                    seg_result.class_name = "未知";
                }
                
                // 获取边界框
                if (item.points.size() >= 2) {
                    float x1 = item.points[0].x;
                    float y1 = item.points[0].y;
                    float x2 = item.points[1].x;
                    float y2 = item.points[1].y;
                    
                    seg_result.bbox = cv::Rect(
                        static_cast<int>(x1), static_cast<int>(y1),
                        static_cast<int>(x2 - x1), static_cast<int>(y2 - y1)
                    );
                }
                
                // 获取分割轮廓
                if (!item.mask.empty()) {
                    seg_result.contours = item.mask;
                    
                    // 创建掩码图像
                    seg_result.mask = cv::Mat::zeros(image.size(), CV_8UC1);
                    cv::fillPoly(seg_result.mask, seg_result.contours, cv::Scalar(255));
                }
                
                results.push_back(seg_result);
            }
        }
        
        return results;
    }
};
```

## 异常检测算法

### ANOMALIB 异常检测示例

```cpp
class AnomalyDetector {
private:
    AIRuntimeInterface* runtime_;
    int model_id_;
    
public:
    AnomalyDetector() : runtime_(GetAIRuntime()), model_id_(30) {}
    
    bool Initialize(const std::string& model_path) {
        stAIConfigInfo config;
        config.preProcessThreadCnt = 4;
        config.inferThreadCnt = 2;
        config.usePinMemory = true;
        
        if (runtime_->InitRuntime(config) != E_OK) {
            return false;
        }
        
        stAIModelInfo modelInfo;
        modelInfo.modelId = model_id_;
        modelInfo.modelName = "anomaly_detector";
        modelInfo.modelPath = model_path;
        modelInfo.modelBackend = "tensorrt";
        modelInfo.algoType = ANOMALIB;
        
        // 设置异常检测参数
        modelInfo.inferParam.confidenceThreshold = 0.5f;   // 异常阈值
        modelInfo.inferParam.gpuId = 0;
        
        return runtime_->CreateModle(modelInfo) == E_OK;
    }
    
    struct AnomalyResult {
        float anomaly_score;      // 异常分数
        bool is_anomaly;          // 是否异常
        cv::Mat heat_map;         // 异常热力图
        cv::Mat reconstructed;    // 重构图像
        cv::Rect anomaly_region;  // 异常区域
    };
    
    AnomalyResult DetectAnomaly(const cv::Mat& image, float threshold = 0.5f) {
        AnomalyResult result;
        result.anomaly_score = 0.0f;
        result.is_anomaly = false;
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {image};
        
        ModelResultPtr model_result = runtime_->RunInferTask(task);
        
        if (!model_result || model_result->itemList.empty()) {
            return result;
        }
        
        // 处理异常检测结果
        for (const auto& itemList : model_result->itemList) {
            for (const auto& item : itemList) {
                result.anomaly_score = item.confidence;
                result.is_anomaly = (result.anomaly_score > threshold);
                
                // 获取异常区域
                if (item.points.size() >= 2) {
                    float x1 = item.points[0].x;
                    float y1 = item.points[0].y;
                    float x2 = item.points[1].x;
                    float y2 = item.points[1].y;
                    
                    result.anomaly_region = cv::Rect(
                        static_cast<int>(x1), static_cast<int>(y1),
                        static_cast<int>(x2 - x1), static_cast<int>(y2 - y1)
                    );
                }
                
                // 获取热力图
                if (!item.heatMap.empty()) {
                    result.heat_map = item.heatMap.clone();
                }
                
                // 获取重构图像
                if (!item.msae_img.empty()) {
                    result.reconstructed = item.msae_img.clone();
                }
                
                break;  // 通常只有一个结果
            }
        }
        
        return result;
    }
};
```

## 旋转目标检测

### YOLOv8 OBB 检测示例

```cpp
class YOLOv8OBBDetector {
private:
    AIRuntimeInterface* runtime_;
    int model_id_;
    std::vector<std::string> class_names_;
    
public:
    YOLOv8OBBDetector() : runtime_(GetAIRuntime()), model_id_(40) {
        class_names_ = {"飞机", "船舶", "储罐", "桥梁", "车辆"};
    }
    
    bool Initialize(const std::string& model_path) {
        stAIConfigInfo config;
        config.preProcessThreadCnt = 6;
        config.inferThreadCnt = 2;
        config.usePinMemory = true;
        
        if (runtime_->InitRuntime(config) != E_OK) {
            return false;
        }
        
        stAIModelInfo modelInfo;
        modelInfo.modelId = model_id_;
        modelInfo.modelName = "yolov8_obb_detector";
        modelInfo.modelPath = model_path;
        modelInfo.modelBackend = "tensorrt";
        modelInfo.algoType = YOLO8_OBB;
        
        // 设置 OBB 检测参数
        modelInfo.inferParam.confidenceThreshold = 0.5f;
        modelInfo.inferParam.nmsThreshold = 0.4f;
        modelInfo.inferParam.maxObjectNums = 500;
        modelInfo.inferParam.gpuId = 0;
        
        return runtime_->CreateModle(modelInfo) == E_OK;
    }
    
    struct OBBResult {
        int class_id;
        std::string class_name;
        float confidence;
        cv::Point2f center;     // 中心点
        cv::Size2f size;        // 宽高
        float angle;            // 旋转角度（弧度）
        std::vector<cv::Point2f> corners;  // 四个角点
    };
    
    std::vector<OBBResult> DetectOBB(const cv::Mat& image) {
        std::vector<OBBResult> results;
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = model_id_;
        task->taskId = static_cast<int>(std::time(nullptr));
        task->imageData = {image};
        
        ModelResultPtr result = runtime_->RunInferTask(task);
        
        if (!result || result->itemList.empty()) {
            return results;
        }
        
        // 处理 OBB 检测结果
        for (const auto& itemList : result->itemList) {
            for (const auto& item : itemList) {
                if (item.points.size() >= 2) {
                    OBBResult obb_result;
                    obb_result.class_id = item.code;
                    obb_result.confidence = item.confidence;
                    obb_result.angle = item.angle;
                    
                    // 获取类别名称
                    if (obb_result.class_id >= 0 && obb_result.class_id < class_names_.size()) {
                        obb_result.class_name = class_names_[obb_result.class_id];
                    } else {
                        obb_result.class_name = "未知";
                    }
                    
                    // 获取中心点和尺寸
                    obb_result.center = cv::Point2f(item.points[0].x, item.points[0].y);
                    obb_result.size = cv::Size2f(item.points[1].x, item.points[1].y);
                    
                    // 计算四个