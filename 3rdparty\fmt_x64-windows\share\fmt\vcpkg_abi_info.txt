cmake 3.25.1
features core
fix-format-conflict.patch 2f994832581a94b22493dfc56a2ec11cc99abaaec368d1543a66e96e33bca626
fix-write-batch.patch d71ed7679da338ca7cc25fb1f7e8af51f15c43dfde26dde2a5904927fe9e7994
portfile.cmake 8b383837254930b3ef57bf741e3f1b97aa7746231c88125d38f126dcb1752ae6
ports.cmake 47a3510fcec56fae26f4fb082afd972a70a04e26efa73e2de69123139500f02d
post_build_checks 2
powershell 7.2.8
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-2b9f5a18de013332e7068fafe850651fd91434ba2a08aae552b8565a2236f81b-83d9dacea1b6b65a04acd5f32bc36b9e3c637eb0
usage ece096518c58df58cfc60f5ad8120d248a86383482455f539d91b8ca6eac14a7
vcpkg-cmake 3ca4909ccff51ff010c1af28213ddddee8a83fbff46f6fd3ef3ee14f4ec78c49
vcpkg-cmake-config 9eac6a999bcac02dbba06e3951ca85857bf7e9d259f4d1e11cd891c1ff8cffbf
vcpkg.json 75a7614ba0824c4f25e3dda2cdfeb4164237d3aad2f21a33310bc0fbdd095ed2
vcpkg_copy_pdbs d57e4f196c82dc562a9968c6155073094513c31e2de475694143d3aa47954b1c
vcpkg_fixup_pkgconfig e61be31d6539cc056e70b4f6c124e8da1de30e501a438e8cf358379ce744680d
vcpkg_from_git 8f27bff0d01c6d15a3e691758df52bfbb0b1b929da45c4ebba02ef76b54b1881
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string d43c8699ce27e25d47367c970d1c546f6bc36b6df8fb0be0c3986eb5830bd4f1
