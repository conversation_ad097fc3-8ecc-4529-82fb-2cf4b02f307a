cmake 3.25.1
features core
fmt 011960f78c6b9bc70396cd71a066ea7bff998592eb020bc6da3c50a971026284
fmt-header.patch 365ece45e9df3bea580c106f7415a5dc13de22365c1671c1a7e5e5afe5461dc0
portfile.cmake 9b37fab66aca66c53722e019ee571c5063a5933d555e95f60d48470875dbb61a
ports.cmake 47a3510fcec56fae26f4fb082afd972a70a04e26efa73e2de69123139500f02d
post_build_checks 2
powershell 7.2.8
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-2b9f5a18de013332e7068fafe850651fd91434ba2a08aae552b8565a2236f81b-83d9dacea1b6b65a04acd5f32bc36b9e3c637eb0
vcpkg-cmake 3ca4909ccff51ff010c1af28213ddddee8a83fbff46f6fd3ef3ee14f4ec78c49
vcpkg-cmake-config 9eac6a999bcac02dbba06e3951ca85857bf7e9d259f4d1e11cd891c1ff8cffbf
vcpkg.json bdfe8aea499307fe3a026725a74ff78a276d0cf9fc75d6bdf63cbadc30bef1e6
vcpkg_check_features 3cdc889b7e6965ad5ddab803060aed2019c0177b6f314c7a5a947c01efa4db60
vcpkg_copy_pdbs d57e4f196c82dc562a9968c6155073094513c31e2de475694143d3aa47954b1c
vcpkg_fixup_pkgconfig e61be31d6539cc056e70b4f6c124e8da1de30e501a438e8cf358379ce744680d
vcpkg_from_git 8f27bff0d01c6d15a3e691758df52bfbb0b1b929da45c4ebba02ef76b54b1881
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string d43c8699ce27e25d47367c970d1c546f6bc36b6df8fb0be0c3986eb5830bd4f1
