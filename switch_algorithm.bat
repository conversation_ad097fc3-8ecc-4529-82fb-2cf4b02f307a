@echo off
chcp 65001 >nul
echo 🔄 AIRuntime 算法切换工具
echo ================================================

if "%1"=="" goto :show_help
if "%1"=="yolo" goto :switch_yolo
if "%1"=="ocr" goto :switch_ocr
if "%1"=="anomaly" goto :switch_anomaly
if "%1"=="msae" goto :switch_msae
if "%1"=="all" goto :switch_all
if "%1"=="status" goto :show_status
goto :show_help

:switch_yolo
echo 🎯 切换到 YOLO 目标检测...
powershell -Command "(Get-Content 'sample\trt_test\trt_test_main.cpp') -replace '//\s*test_yolo\(\);', 'test_yolo();' -replace 'test_trt_ocr\(\);', '// test_trt_ocr();' -replace 'test_mase\(\);', '// test_mase();' -replace 'test_anomalib\(\);', '// test_anomalib();' | Set-Content 'sample\trt_test\trt_test_main.cpp'"
echo ✅ 已切换到 YOLO 算法
goto :build_and_run

:switch_ocr
echo 🔤 切换到 OCR 文字识别...
powershell -Command "(Get-Content 'sample\trt_test\trt_test_main.cpp') -replace 'test_yolo\(\);', '// test_yolo();' -replace '//\s*test_trt_ocr\(\);', 'test_trt_ocr();' -replace 'test_mase\(\);', '// test_mase();' -replace 'test_anomalib\(\);', '// test_anomalib();' | Set-Content 'sample\trt_test\trt_test_main.cpp'"
echo ✅ 已切换到 OCR 算法
echo ⚠️  注意: 需要 OCR 模型文件 (ocr_det.trtmodel, ocr_rec.trtmodel)
goto :build_and_run

:switch_anomaly
echo 🔍 切换到异常检测...
powershell -Command "(Get-Content 'sample\trt_test\trt_test_main.cpp') -replace 'test_yolo\(\);', '// test_yolo();' -replace 'test_trt_ocr\(\);', '// test_trt_ocr();' -replace 'test_mase\(\);', '// test_mase();' -replace '//\s*test_anomalib\(\);', 'test_anomalib();' | Set-Content 'sample\trt_test\trt_test_main.cpp'"
echo ✅ 已切换到异常检测算法
echo ⚠️  注意: 需要异常检测模型文件 (stfpm.trtmodel)
goto :build_and_run

:switch_msae
echo 🎯 切换到 MSAE 算法...
powershell -Command "(Get-Content 'sample\trt_test\trt_test_main.cpp') -replace 'test_yolo\(\);', '// test_yolo();' -replace 'test_trt_ocr\(\);', '// test_trt_ocr();' -replace '//\s*test_mase\(\);', 'test_mase();' -replace 'test_anomalib\(\);', '// test_anomalib();' | Set-Content 'sample\trt_test\trt_test_main.cpp'"
echo ✅ 已切换到 MSAE 算法
echo ⚠️  注意: 需要 MSAE 模型文件 (msae_hgz_a.trtmodel)
goto :build_and_run

:switch_all
echo 🔄 启用所有算法...
powershell -Command "(Get-Content 'sample\trt_test\trt_test_main.cpp') -replace '//\s*test_yolo\(\);', 'test_yolo();' -replace '//\s*test_trt_ocr\(\);', 'test_trt_ocr();' -replace '//\s*test_mase\(\);', 'test_mase();' -replace '//\s*test_anomalib\(\);', 'test_anomalib();' | Set-Content 'sample\trt_test\trt_test_main.cpp'"
echo ✅ 已启用所有算法
echo ⚠️  注意: 需要所有模型文件
goto :build_and_run

:show_status
echo 📊 当前算法状态:
echo.
findstr /n "test_.*();" sample\trt_test\trt_test_main.cpp
echo.
echo 📁 可用模型文件:
if exist "sample\models\" (
    dir /b "sample\models\*.engine" "sample\models\*.trtmodel" "sample\models\*.onnx" 2>nul
    if %errorlevel%==1 echo    未找到模型文件
) else (
    echo ❌ 模型目录不存在
)
goto :end

:build_and_run
echo.
echo 🔨 重新编译项目...
xmake
if %errorlevel%==0 (
    echo ✅ 编译成功
    echo.
    echo 🚀 运行测试...
    xmake run trt_test
    if %errorlevel%==0 (
        echo.
        echo ✅ 测试完成！
        echo 📁 结果保存在: sample\test_data\output\
    ) else (
        echo ❌ 测试失败
    )
) else (
    echo ❌ 编译失败
)
goto :end

:show_help
echo 用法: switch_algorithm.bat [算法名称]
echo.
echo 可用算法:
echo   yolo      - YOLO 目标检测 (✅ 可用)
echo   ocr       - OCR 文字识别 (需要模型)
echo   anomaly   - 异常检测 (需要模型)
echo   msae      - MSAE 算法 (需要模型)
echo   all       - 启用所有算法
echo   status    - 显示当前状态
echo.
echo 示例:
echo   switch_algorithm.bat yolo     # 切换到 YOLO
echo   switch_algorithm.bat ocr      # 切换到 OCR
echo   switch_algorithm.bat status   # 查看状态
echo.
echo 💡 提示:
echo   - YOLO 算法已有模型文件，可直接使用
echo   - 其他算法需要相应的模型文件
echo   - 模型文件应放在 sample\models\ 目录中

:end
echo.
pause
