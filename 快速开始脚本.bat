@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    TensorRT YOLO 推理系统快速开始
echo ========================================
echo.

:: 设置项目路径
set PROJECT_ROOT=F:\algo_ai
set MODELS_DIR=%PROJECT_ROOT%\sample\models
set TEST_DATA_DIR=%PROJECT_ROOT%\sample\test_data
set BUILD_DIR=%PROJECT_ROOT%\build\windows\x64\release

:: 检查项目目录
if not exist "%PROJECT_ROOT%" (
    echo 错误：项目目录不存在 %PROJECT_ROOT%
    pause
    exit /b 1
)

cd /d "%PROJECT_ROOT%"

echo 1. 检查环境...
echo    项目根目录: %PROJECT_ROOT%
echo    模型目录: %MODELS_DIR%
echo    测试数据目录: %TEST_DATA_DIR%
echo.

:: 菜单选择
:menu
echo ========================================
echo 请选择操作：
echo 1. 构建项目
echo 2. ONNX转Engine
echo 3. 运行推理测试
echo 4. 查看测试结果
echo 5. 性能测试
echo 6. 清理构建
echo 0. 退出
echo ========================================
set /p choice=请输入选择 (0-6): 

if "%choice%"=="1" goto build_project
if "%choice%"=="2" goto convert_onnx
if "%choice%"=="3" goto run_inference
if "%choice%"=="4" goto view_results
if "%choice%"=="5" goto benchmark
if "%choice%"=="6" goto clean_build
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:build_project
echo.
echo ========================================
echo 2. 构建项目...
echo ========================================
echo 清理之前的构建...
xmake clean

echo 构建AIFramework库...
xmake build AIFramework
if !errorlevel! neq 0 (
    echo 错误：AIFramework构建失败
    pause
    goto menu
)

echo 构建onnx2engine工具...
xmake build onnx2engine
if !errorlevel! neq 0 (
    echo 错误：onnx2engine构建失败
    pause
    goto menu
)

echo 构建trt_test测试程序...
xmake build trt_test
if !errorlevel! neq 0 (
    echo 错误：trt_test构建失败
    pause
    goto menu
)

echo 复制运行时依赖...
if exist "E:\TensorRT-8.6.1.6\lib\*.dll" (
    copy "E:\TensorRT-8.6.1.6\lib\*.dll" "%BUILD_DIR%\" >nul 2>&1
)
if exist "E:\cudnn-windows-x86_64-8.9.0.131_cuda12\lib\x64\*.dll" (
    copy "E:\cudnn-windows-x86_64-8.9.0.131_cuda12\lib\x64\*.dll" "%BUILD_DIR%\" >nul 2>&1
)

echo ✓ 项目构建完成！
echo.
pause
goto menu

:convert_onnx
echo.
echo ========================================
echo 3. ONNX转Engine
echo ========================================

:: 检查ONNX文件
if not exist "%MODELS_DIR%\yolov5s.onnx" (
    echo 错误：找不到ONNX文件 %MODELS_DIR%\yolov5s.onnx
    echo 请确保ONNX模型文件存在
    pause
    goto menu
)

echo 找到ONNX文件: %MODELS_DIR%\yolov5s.onnx

echo.
echo 选择转换选项：
echo 1. FP32精度（默认）
echo 2. FP16精度（推荐，速度更快）
echo 3. 自定义参数
set /p conv_choice=请选择 (1-3): 

if "%conv_choice%"=="1" (
    set "conv_params="
    set "output_suffix="
) else if "%conv_choice%"=="2" (
    set "conv_params=--fp16"
    set "output_suffix=_fp16"
) else if "%conv_choice%"=="3" (
    set /p batch_size=输入批处理大小 (默认1): 
    set /p workspace=输入工作空间大小MB (默认1024): 
    set /p use_fp16=使用FP16精度? (y/n): 
    
    set "conv_params="
    if not "%batch_size%"=="" set "conv_params=!conv_params! --batch %batch_size%"
    if not "%workspace%"=="" set "conv_params=!conv_params! --workspace %workspace%"
    if /i "%use_fp16%"=="y" (
        set "conv_params=!conv_params! --fp16"
        set "output_suffix=_custom_fp16"
    ) else (
        set "output_suffix=_custom"
    )
) else (
    echo 无效选择，使用默认参数
    set "conv_params="
    set "output_suffix="
)

echo.
echo 开始转换...
"%BUILD_DIR%\onnx2engine.exe" "%MODELS_DIR%\yolov5s.onnx" "%MODELS_DIR%\yolov5s%output_suffix%.engine" %conv_params%

if !errorlevel! equ 0 (
    echo ✓ 转换完成！
    echo Engine文件: %MODELS_DIR%\yolov5s%output_suffix%.engine
) else (
    echo ✗ 转换失败！
)
echo.
pause
goto menu

:run_inference
echo.
echo ========================================
echo 4. 运行推理测试
echo ========================================

:: 检查Engine文件
set "engine_found=0"
if exist "%MODELS_DIR%\yolov5s.engine" set "engine_found=1"
if exist "%MODELS_DIR%\yolov5s_fp16.engine" set "engine_found=1"

if "%engine_found%"=="0" (
    echo 错误：找不到Engine文件
    echo 请先执行ONNX转Engine操作
    pause
    goto menu
)

:: 检查测试图片
if not exist "%TEST_DATA_DIR%\test\*.jpg" (
    echo 错误：找不到测试图片 %TEST_DATA_DIR%\test\
    pause
    goto menu
)

echo 开始推理测试...
cd /d "%BUILD_DIR%"
.\trt_test.exe

if !errorlevel! equ 0 (
    echo ✓ 推理测试完成！
    echo 结果保存在: %TEST_DATA_DIR%\output\
) else (
    echo ✗ 推理测试失败！
)
echo.
pause
goto menu

:view_results
echo.
echo ========================================
echo 5. 查看测试结果
echo ========================================

if not exist "%TEST_DATA_DIR%\output\*.jpg" (
    echo 没有找到测试结果图片
    echo 请先运行推理测试
    pause
    goto menu
)

echo 测试结果图片：
for %%f in ("%TEST_DATA_DIR%\output\*.jpg") do (
    echo   %%~nxf
)

echo.
set /p open_folder=是否打开结果文件夹? (y/n): 
if /i "%open_folder%"=="y" (
    explorer "%TEST_DATA_DIR%\output"
)

echo.
echo 查看最新日志文件...
if exist "%BUILD_DIR%\log\*.log" (
    for /f %%f in ('dir /b /o-d "%BUILD_DIR%\log\*.log"') do (
        echo 最新日志: %%f
        type "%BUILD_DIR%\log\%%f"
        goto log_done
    )
    :log_done
)

pause
goto menu

:benchmark
echo.
echo ========================================
echo 6. 性能测试
echo ========================================
echo 此功能需要自定义实现
echo 可以参考文档中的性能测试代码示例
pause
goto menu

:clean_build
echo.
echo ========================================
echo 7. 清理构建
echo ========================================
echo 清理构建文件...
xmake clean
echo ✓ 清理完成！
pause
goto menu

:exit
echo.
echo 感谢使用 TensorRT YOLO 推理系统！
echo.
pause
exit /b 0
