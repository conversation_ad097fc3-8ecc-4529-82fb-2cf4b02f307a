#include <iostream>
#include <exception>
#include <Kernel/IKernelShare.h>
#include "CPlugin.h"

CPlugin::CPlugin(const char* pstrKey, IPlugin* pIHost)
    : base(pstrKey, pIHost)
    , m_pMainWin(NULL)
{



}
CPlugin::~CPlugin()
{

}

int CPlugin::OnInitialize()
{
    int iRet = base::OnInitialize();


    return iRet;
}

int CPlugin::OnUninitialize()
{
    int iRet = base::OnUninitialize();


    return iRet;
}

int CPlugin::OnStartup()
{
    int iRet = base::OnStartup();
    return iRet;
}


int CPlugin::OnShutdown()
{
    int iRet = base::OnShutdown();
    return iRet;
}


