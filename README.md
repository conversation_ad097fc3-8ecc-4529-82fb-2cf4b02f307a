# AIRuntime 项目文档

欢迎来到 AIRuntime 项目！这是一个高性能的 AI 推理运行时框架，支持多种深度学习模型的推理任务。

## 📚 文档导航

### 🚀 [新人上手指南](新人上手指南.md)
**适合人群**: 新加入项目的开发者
**内容包括**:
- 项目概述和核心特性
- 完整的项目架构说明
- 开发环境搭建步骤
- 快速开始教程
- 详细的使用示例
- 开发指南和最佳实践
- 常见问题解决方案

### 🏗️ [技术架构文档](技术架构文档.md)
**适合人群**: 架构师、高级开发者
**内容包括**:
- 详细的系统架构设计
- 核心组件深度解析
- 线程模型和内存管理
- 性能优化策略
- 扩展性和容错设计
- 安全性考虑
- 部署架构方案

### ⚡ [开发者快速参考](开发者快速参考.md)
**适合人群**: 日常开发使用
**内容包括**:
- 常用 API 速查表
- 数据结构参考
- 算法类型对照表
- 错误码说明
- 代码模板集合
- 调试技巧
- 构建命令速查

### 🎯 [算法使用示例](算法使用示例.md)
**适合人群**: 算法开发者、应用集成者
**内容包括**:
- 图像分类完整示例
- 目标检测详细实现
- OCR 文字识别流程
- 图像分割应用案例
- 异常检测实战代码
- 旋转目标检测示例
- 批处理推理优化
- 性能调优技巧

### 🎨 [设计模式分析](设计模式分析.md)
**适合人群**: 架构师、高级开发者、代码审查者
**内容包括**:
- 创建型模式详解（单例、工厂方法、抽象工厂）
- 结构型模式应用（外观、适配器、桥接）
- 行为型模式实现（观察者、策略、模板方法、命令）
- 并发模式设计（生产者-消费者、线程池、Future/Promise）
- 架构模式分析（分层架构、插件架构）
- 设计模式协作关系和最佳实践
- 代码示例和 UML 图解

## 🎯 项目特色

- **🚄 高性能**: 基于 TensorRT 和 ONNX Runtime 的优化推理
- **🔄 异步处理**: 生产者-消费者模式的高效任务调度
- **🎨 多算法支持**: 分类、检测、OCR、分割、异常检测等
- **🌐 跨平台**: 支持 Windows 和 Linux 操作系统
- **🔧 易于集成**: 简洁统一的 C API 接口
- **📈 可扩展**: 插件化架构，支持自定义算法

## 🏃‍♂️ 快速开始

### 1. 环境要求
- **Windows**: Visual Studio 2019/2022, CUDA 11.8+
- **Linux**: GCC 7.0+, CUDA 11.8+
- **构建工具**: XMake

### 2. 编译项目
```bash
# 配置构建环境
xmake f --vs=2022 --vs_toolset=14.38  # Windows
xmake f                                # Linux

# 编译项目
xmake
```

### 3. 运行示例
```bash
# 运行 DLL 测试
xmake run test_dll

# 运行 TensorRT 测试
xmake run trt_test
```

### 4. 基础使用
```cpp
#include "AIRuntimeInterface.h"

// 初始化运行时
AIRuntimeInterface* runtime = GetAIRuntime();
stAIConfigInfo config;
runtime->InitRuntime(config);

// 创建模型
stAIModelInfo modelInfo;
modelInfo.modelId = 0;
modelInfo.modelPath = "model.trtmodel";
modelInfo.algoType = YOLO8;
runtime->CreateModle(modelInfo);

// 执行推理
cv::Mat image = cv::imread("test.jpg");
TaskInfoPtr task = std::make_shared<stTaskInfo>();
task->modelId = 0;
task->imageData = {image};
ModelResultPtr result = runtime->RunInferTask(task);

// 清理资源
runtime->DestoryRuntime();
```

## 📁 项目结构

```
algo_ai/
├── 📂 3rdparty/           # 第三方依赖库
├── 📂 include/            # 头文件目录
│   ├── 📂 public/         # 公共接口
│   └── 📂 private/        # 内部实现
├── 📂 src/                # 源代码目录
│   ├── 📂 airuntime/      # 核心运行时
│   ├── 📂 trt/            # TensorRT 后端
│   └── 📂 ort/            # ONNX Runtime 后端
├── 📂 sample/             # 示例代码
├── 📂 install/            # 安装输出
└── 📄 xmake.lua           # 构建配置
```

## 🔧 支持的算法

| 算法类型 | 描述 | 后端支持 | 示例代码 |
|---------|------|----------|----------|
| 图像分类 | 对整张图像进行分类 | TensorRT, ONNX Runtime | [查看示例](算法使用示例.md#图像分类算法) |
| 目标检测 | YOLO 系列目标检测 | TensorRT, ONNX Runtime | [查看示例](算法使用示例.md#目标检测算法) |
| OCR 识别 | 光学字符识别 | TensorRT, ONNX Runtime | [查看示例](算法使用示例.md#ocr-文字识别) |
| 图像分割 | 语义分割和实例分割 | TensorRT | [查看示例](算法使用示例.md#图像分割算法) |
| 异常检测 | 工业异常检测 | TensorRT | [查看示例](算法使用示例.md#异常检测算法) |
| 旋转检测 | 带角度的目标检测 | TensorRT | [查看示例](算法使用示例.md#旋转目标检测) |

## 🚀 性能特性

- **异步推理**: 支持高并发推理任务
- **批处理优化**: 自动批处理提高吞吐量
- **内存优化**: 智能内存池和缓存管理
- **GPU 加速**: 充分利用 CUDA 和 TensorRT 优化
- **多线程**: 预处理、推理、后处理并行执行

## 🛠️ 开发工具

### 构建系统
- **XMake**: 现代化的构建工具
- **跨平台**: 统一的构建配置

### 调试工具
- **日志系统**: 基于 spdlog 的分级日志
- **性能监控**: 内置时间统计和 GPU 监控
- **内存检测**: 智能指针和 RAII 设计

### 测试框架
- **单元测试**: 基于 doctest 框架
- **集成测试**: 端到端测试用例
- **性能测试**: 基准测试和压力测试

## 📊 架构图

### 整体架构
```mermaid
graph TB
    A[应用程序] --> B[AIRuntime接口]
    B --> C[核心运行时]
    C --> D[任务队列管理]
    C --> E[推理引擎层]
    E --> F[TensorRT后端]
    E --> G[ONNX Runtime后端]
    F --> H[算法实现]
    G --> H
    H --> I[CUDA/OpenCV]
```

### 推理流程
```mermaid
sequenceDiagram
    participant App as 应用程序
    participant Runtime as AIRuntime
    participant Queue as 任务队列
    participant Engine as 推理引擎
    
    App->>Runtime: 提交推理任务
    Runtime->>Queue: 任务入队
    Queue->>Engine: 执行推理
    Engine->>Engine: 前处理→推理→后处理
    Engine->>Runtime: 返回结果
    Runtime->>App: 回调通知
```

## 🤝 贡献指南

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

### 代码规范
- 使用 C++17 标准
- 遵循项目命名规范
- 添加必要的注释和文档
- 确保测试覆盖率

### 提交规范
- 清晰的提交信息
- 单一功能的提交
- 包含相关测试用例

## 📞 获取帮助

### 文档资源
- 📖 [新人上手指南](新人上手指南.md) - 详细的入门教程
- 🏗️ [技术架构文档](技术架构文档.md) - 深入的架构分析
- ⚡ [开发者快速参考](开发者快速参考.md) - 日常开发参考
- 🎯 [算法使用示例](算法使用示例.md) - 完整的算法实现示例
- 🎨 [设计模式分析](设计模式分析.md) - 项目设计模式深度解析

### 社区支持
- 💬 提交 Issue 报告问题
- 🔧 参与 Pull Request 讨论
- 📧 联系项目维护者

### 相关资源
- [TensorRT 官方文档](https://docs.nvidia.com/deeplearning/tensorrt/)
- [ONNX Runtime 文档](https://onnxruntime.ai/docs/)
- [OpenCV 文档](https://docs.opencv.org/)

## 📄 许可证

本项目采用开源许可证，详情请查看 LICENSE 文件。

## 🎉 致谢

感谢所有为 AIRuntime 项目做出贡献的开发者和社区成员！

---

**开始你的 AIRuntime 开发之旅吧！** 🚀

### 📋 学习路径建议

1. **新手入门**: 从 [新人上手指南](新人上手指南.md) 开始，了解项目基础
2. **深入理解**: 阅读 [技术架构文档](技术架构文档.md)，掌握系统设计
3. **设计思想**: 学习 [设计模式分析](设计模式分析.md)，理解架构设计理念
4. **实践应用**: 参考 [算法使用示例](算法使用示例.md)，实现具体功能
5. **日常开发**: 使用 [开发者快速参考](开发者快速参考.md) 提高效率

### 🔍 快速导航

| 需求场景 | 推荐文档 | 关键内容 |
|---------|----------|----------|
| 项目入门 | [新人上手指南](新人上手指南.md) | 环境搭建、快速开始 |
| 架构理解 | [技术架构文档](技术架构文档.md) | 系统设计、性能优化 |
| 设计理念 | [设计模式分析](设计模式分析.md) | 设计模式、架构思想 |
| 算法集成 | [算法使用示例](算法使用示例.md) | 完整代码示例 |
| API 查询 | [开发者快速参考](开发者快速参考.md) | 接口速查、错误码 |
| 问题排查 | [新人上手指南](新人上手指南.md#常见问题解决) | 故障诊断、解决方案 |
