@echo off
chcp 65001 >nul
echo 🤖 AIRuntime 测试运行器
echo ================================================

if "%1"=="" goto :show_help
if "%1"=="help" goto :show_help
if "%1"=="yolo" goto :run_yolo
if "%1"=="ocr" goto :run_ocr
if "%1"=="clean" goto :clean_output
if "%1"=="models" goto :show_models
if "%1"=="info" goto :show_info
goto :show_help

:run_yolo
echo 🚀 运行 YOLO 目标检测测试...
xmake run trt_test
if %errorlevel%==0 (
    echo ✅ YOLO 测试成功完成！
    echo 📁 结果保存在: sample\test_data\output\
    goto :show_results
) else (
    echo ❌ YOLO 测试失败
)
goto :end

:run_ocr
echo 🔤 OCR 测试功能开发中...
echo 💡 提示: 请使用 Python 脚本进行 OCR 测试
echo    python test_runner.py ocr
goto :end

:clean_output
echo 🧹 清理输出目录...
if exist "sample\test_data\output\*.jpg" (
    del /q "sample\test_data\output\*.jpg"
    echo ✅ 已清理输出文件
) else (
    echo 📁 输出目录为空
)
goto :end

:show_models
echo 🤖 可用的模型文件:
if exist "sample\models\" (
    dir /b "sample\models\*.engine" "sample\models\*.onnx" 2>nul
    if %errorlevel%==1 echo    未找到模型文件
) else (
    echo ❌ 模型目录不存在
)
goto :end

:show_info
echo 📊 项目信息:
echo.
call :show_models
echo.
echo 🖼️  测试图片:
if exist "sample\test_data\test\" (
    for /f %%i in ('dir /b "sample\test_data\test\*.jpg" 2^>nul ^| find /c /v ""') do echo    共 %%i 张测试图片
) else (
    echo ❌ 测试图片目录不存在
)
echo.
call :show_results
goto :end

:show_results
echo 📊 输出结果:
if exist "sample\test_data\output\" (
    for /f %%i in ('dir /b "sample\test_data\output\*.jpg" 2^>nul ^| find /c /v ""') do echo    共生成 %%i 张结果图片
    echo 📁 结果目录: sample\test_data\output\
) else (
    echo 📁 暂无输出结果
)
goto :eof

:show_help
echo 用法: test.bat [命令]
echo.
echo 可用命令:
echo   yolo     - 运行 YOLO 目标检测测试
echo   ocr      - 运行 OCR 文字识别测试 (开发中)
echo   clean    - 清理输出目录
echo   models   - 显示可用模型
echo   info     - 显示项目信息
echo   help     - 显示此帮助信息
echo.
echo 示例:
echo   test.bat yolo    # 运行 YOLO 测试
echo   test.bat clean   # 清理输出
echo   test.bat info    # 查看项目信息
echo.
echo 💡 提示: 
echo   - 确保已编译项目 (xmake)
echo   - 结果图片保存在 sample\test_data\output\
echo   - 使用 Python 脚本获得更多功能: python test_runner.py

:end
echo.
echo 完成！
pause
