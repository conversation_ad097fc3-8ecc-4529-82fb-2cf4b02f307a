{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/nlo<PERSON>-json-x64-windows-3.11.2-a792d4e4-ffe4-47b0-9824-50cd656e2343", "name": "n<PERSON><PERSON>-json:x64-windows@3.11.2 5e8aa477fecc6cd5ae1bad5ad317ab0f1d402e3e4a1dab4a3fb776a37e30f55b", "creationInfo": {"creators": ["Tool: vcpkg-8a88d63f241d391772fbde69af9cab96c3c64c75"], "created": "2023-02-10T02:33:38Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "SPDXID": "SPDXRef-port", "versionInfo": "3.11.2", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/nlo<PERSON>-json", "homepage": "https://github.com/nlohmann/json", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "JSON for Modern C++", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "nlohmann-json:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "5e8aa477fecc6cd5ae1bad5ad317ab0f1d402e3e4a1dab4a3fb776a37e30f55b", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-1", "name": "n<PERSON><PERSON>/json", "downloadLocation": "git+https://github.com/nlohmann/json@v3.11.2", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "70097c9bcd7a91254acbd41b8b68a6aaa371fc2dd7011f472917f69f1e2d2986155a0339dad791699d542e4a3be44dc49ae72ff73d0ee0ea4b34183296ce19a0"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "20e24863e86739ae6c15132133a78948ec819a27fe1acfa09768635e253824b4"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "ec6460d4688d7908d8adc698700f77639013641f1ab27066e9324c9ca11276c3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "f94cff0a5dd2cbfe3c131f86f210fa8861ccd3daa303d6b9f7b0514a8a4bc218"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}