
# Created by https://www.toptal.com/developers/gitignore/api/c++
# Edit at https://www.toptal.com/developers/gitignore?templates=c++

### C++ ###
# Prerequisites
*.d


### Xmake cache
.xmake/
build/
models/
data/
vs2022/
#data/
*.zip
.vscode/
.vs/
.vsxmake2022/

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
#*.lib

# Executables
*.exe
*.out
*.app

# End of https://www.toptal.com/developers/gitignore/api/c++

#cpp
.history
