# AIRuntime 项目新人上手指南

## 项目概述

AIRuntime 是一个高性能的 AI 推理运行时框架，支持多种深度学习模型的推理任务。该项目采用 C++ 开发，支持 TensorRT 和 ONNX Runtime 两种推理后端，可以运行在 Windows 和 Linux 平台上。

### 核心特性

- **多后端支持**: 支持 TensorRT 和 ONNX Runtime 推理引擎
- **多算法支持**: 支持分类、目标检测（YOLO系列）、OCR、图像分割、异常检测等多种算法
- **异步推理**: 基于生产者-消费者模式的异步推理架构
- **跨平台**: 支持 Windows 和 Linux 操作系统
- **高性能**: 利用 GPU 加速和内存优化技术
- **易于集成**: 提供简洁的 C API 接口

## 项目架构

### 整体架构图

```mermaid
graph TB
    subgraph "应用层"
        A[用户应用程序]
        B[示例程序]
    end
    
    subgraph "接口层"
        C[AIRuntimeInterface.h]
        D[AIRuntimeDataStruct.h]
        E[AIRuntimeUtils.h]
    end
    
    subgraph "核心运行时"
        F[AIRuntime]
        G[任务队列管理]
        H[结果回调系统]
        I[模型管理器]
    end
    
    subgraph "推理引擎层"
        J[TensorRT 后端]
        K[ONNX Runtime 后端]
    end
    
    subgraph "算法实现层"
        L[分类算法]
        M[YOLO 检测]
        N[OCR 识别]
        O[图像分割]
        P[异常检测]
    end
    
    subgraph "底层依赖"
        Q[CUDA/cuDNN]
        R[OpenCV]
        S[第三方库]
    end
    
    A --> C
    B --> C
    C --> F
    D --> F
    E --> F
    F --> G
    F --> H
    F --> I
    I --> J
    I --> K
    J --> L
    J --> M
    J --> N
    J --> O
    J --> P
    K --> L
    K --> M
    K --> N
    L --> Q
    M --> Q
    N --> Q
    O --> Q
    P --> Q
    F --> R
    F --> S
```

### 推理流程图

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant API as AIRuntimeInterface
    participant Runtime as AIRuntime
    participant Queue as 任务队列
    participant Engine as 推理引擎
    participant Callback as 回调系统
    
    App->>API: InitRuntime(config)
    API->>Runtime: 初始化运行时
    Runtime->>Runtime: 启动工作线程
    
    App->>API: CreateModel(modelInfo)
    API->>Runtime: 创建模型
    Runtime->>Engine: 加载模型
    
    App->>API: CommitInferTask(task)
    API->>Runtime: 提交推理任务
    Runtime->>Queue: 任务入队
    
    Queue->>Engine: 执行推理
    Engine->>Engine: 前处理
    Engine->>Engine: 模型推理
    Engine->>Engine: 后处理
    
    Engine->>Callback: 推理结果
    Callback->>App: OnModelResult(result)
```

### 核心组件说明

#### 1. 接口层 (include/public/)
- **AIRuntimeInterface.h**: 主要的 C API 接口定义
- **AIRuntimeDataStruct.h**: 数据结构定义，包括配置、模型信息、任务信息等
- **AIRuntimeUtils.h**: 工具函数，包括 JSON 处理、时间计算等

#### 2. 核心运行时 (src/airuntime/)
- **AIRuntime.cpp**: 核心运行时实现，管理整个推理流程
- **AIRuntimeInterface.cpp**: C API 接口的具体实现
- **AIAlgoBase.cpp**: 算法基类实现

#### 3. 推理引擎层
- **TensorRT 后端** (src/trt/): 基于 NVIDIA TensorRT 的高性能推理实现
- **ONNX Runtime 后端** (src/ort/): 基于 ONNX Runtime 的跨平台推理实现

#### 4. 算法实现层
支持的算法类型：
- **分类算法** (CLASSIFY): 图像分类
- **目标检测** (YOLOV5, YOLO8): YOLO 系列目标检测
- **OCR** (OCR_DET, OCR_REC, OCR_CLS): 光学字符识别
- **图像分割** (SEGMENT, YOLOV8_SEG): 语义分割和实例分割
- **异常检测** (ANOMALIB): 工业异常检测
- **其他** (MSAE, DEIM, YOLO8_OBB): 特殊算法实现

## 项目目录结构

```
algo_ai/
├── 3rdparty/                    # 第三方依赖库
│   ├── opencv2/                 # OpenCV 库
│   ├── TensorRT-8.6.1.6/       # TensorRT 库
│   ├── cudnn-8.9.6.50/         # cuDNN 库
│   ├── nlohmann-json_x64-windows/ # JSON 处理库
│   ├── spdlog_x64-windows/      # 日志库
│   └── ...
├── HQ_Share/                    # 共享组件
│   └── include/                 # 共享头文件
├── include/                     # 头文件目录
│   ├── public/                  # 公共接口头文件
│   │   ├── AIRuntimeInterface.h
│   │   ├── AIRuntimeDataStruct.h
│   │   └── AIRuntimeUtils.h
│   └── private/                 # 内部实现头文件
│       ├── airuntime/           # 核心运行时头文件
│       ├── trt/                 # TensorRT 相关头文件
│       └── ort/                 # ONNX Runtime 相关头文件
├── src/                         # 源代码目录
│   ├── airuntime/               # 核心运行时实现
│   ├── trt/                     # TensorRT 后端实现
│   │   ├── trt_common/          # TensorRT 通用组件
│   │   ├── trt_app_classification/ # 分类算法实现
│   │   ├── trt_app_yolo/        # YOLO 检测实现
│   │   ├── trt_app_ocr/         # OCR 实现
│   │   └── ...
│   └── ort/                     # ONNX Runtime 后端实现
│       ├── ort_common/          # ONNX Runtime 通用组件
│       ├── ort_app_classification/ # 分类算法实现
│       └── ...
├── sample/                      # 示例代码
│   ├── dll_test/                # DLL 测试示例
│   ├── trt_test/                # TensorRT 测试示例
│   └── test_data/               # 测试数据
├── Plugin/                      # 插件目录
├── install/                     # 安装输出目录
└── xmake.lua                    # 构建配置文件
```

## 开发环境搭建

### 系统要求

#### Windows 环境
- **操作系统**: Windows 10/11 (64位)
- **编译器**: Visual Studio 2019/2022 (MSVC 14.38+)
- **CUDA**: CUDA 11.8+ (如需 GPU 加速)
- **构建工具**: XMake

#### Linux 环境
- **操作系统**: Ubuntu 18.04+ 或其他主流 Linux 发行版
- **编译器**: GCC 7.0+ (支持 C++17)
- **CUDA**: CUDA 11.8+ (如需 GPU 加速)
- **构建工具**: XMake

### 依赖库安装

项目依赖的主要库已包含在 `3rdparty/` 目录中：

1. **OpenCV 4.8.1**: 计算机视觉库
2. **TensorRT 8.6.1.6**: NVIDIA 推理加速库
3. **cuDNN 8.9.6.50**: CUDA 深度神经网络库
4. **nlohmann/json**: JSON 处理库
5. **spdlog**: 高性能日志库
6. **fmt**: 字符串格式化库
7. **concurrent_queue**: 并发队列库

### 构建步骤

#### 1. 安装 XMake
```bash
# Linux
curl -fsSL https://xmake.io/shget.text | bash

# Windows (PowerShell)
Invoke-Expression (Invoke-WebRequest 'https://xmake.io/psget.text' -UseBasicParsing).Content
```

#### 2. 配置构建环境
```bash
# Windows (使用 Visual Studio 2022)
xmake f --vs=2022 --vs_toolset=14.38

# Linux
xmake f
```

#### 3. 编译项目
```bash
# 编译 Debug 版本
xmake

# 编译 Release 版本
xmake f -m release
xmake
```

#### 4. 运行测试
```bash
# 运行 DLL 测试
xmake run test_dll

# 运行 TensorRT 测试
xmake run trt_test
```

## 快速开始

### 基本使用流程

#### 1. 初始化运行时
```cpp
#include "AIRuntimeInterface.h"

// 获取运行时实例
AIRuntimeInterface* runtime = GetAIRuntime();

// 配置运行时参数
stAIConfigInfo config;
config.preProcessThreadCnt = 8;
config.inferThreadCnt = 4;
config.usePinMemory = true;

// 初始化运行时
runtime->InitRuntime(config);
```

#### 2. 创建模型
```cpp
// 配置模型信息
stAIModelInfo modelInfo;
modelInfo.modelId = 0;
modelInfo.modelName = "yolo8_detector";
modelInfo.modelPath = "path/to/model.trtmodel";
modelInfo.modelBackend = "tensorrt";  // 或 "onnxruntime"
modelInfo.algoType = YOLO8;

// 设置推理参数
modelInfo.inferParam.confidenceThreshold = 0.5f;
modelInfo.inferParam.nmsThreshold = 0.4f;
modelInfo.inferParam.maxObjectNums = 1000;
modelInfo.inferParam.gpuId = 0;

// 创建模型
runtime->CreateModle(modelInfo);
```

#### 3. 执行推理

##### 同步推理
```cpp
// 准备输入数据
cv::Mat image = cv::imread("test_image.jpg");
TaskInfoPtr task = std::make_shared<stTaskInfo>();
task->modelId = 0;
task->taskId = 1;
task->imageData = {image};

// 执行同步推理
ModelResultPtr result = runtime->RunInferTask(task);

// 处理结果
for (const auto& itemList : result->itemList) {
    for (const auto& item : itemList) {
        std::cout << "检测到目标: 类别=" << item.code 
                  << ", 置信度=" << item.confidence
                  << ", 位置=(" << item.points[0].x << "," << item.points[0].y 
                  << "," << item.points[1].x << "," << item.points[1].y << ")"
                  << std::endl;
    }
}
```

##### 异步推理
```cpp
// 实现结果监听器
class MyResultListener : public IModelResultListener {
public:
    void OnModelResult(ModelResultPtr result) override {
        // 处理推理结果
        std::cout << "收到异步推理结果, 任务ID: " << result->taskInfo->taskId << std::endl;
        // ... 处理结果逻辑
    }
};

// 注册结果监听器
MyResultListener listener;
runtime->RegisterResultListener(0, &listener);

// 提交异步推理任务
TaskInfoPtr task = std::make_shared<stTaskInfo>();
task->modelId = 0;
task->taskId = 2;
task->imageData = {image};

runtime->CommitInferTask(task);
```

#### 4. 清理资源
```cpp
// 销毁模型
runtime->DestroyModle(0);

// 销毁运行时
runtime->DestoryRuntime();
```

### 支持的算法示例

#### 图像分类
```cpp
stAIModelInfo classifyModel;
classifyModel.modelId = 1;
classifyModel.algoType = CLASSIFY;
classifyModel.modelPath = "classifier.trtmodel";
classifyModel.inferParam.confidenceThreshold = 0.8f;

runtime->CreateModle(classifyModel);

// 推理结果中 item.code 表示分类结果
```

#### OCR 文字识别
```cpp
// OCR 检测模型
stAIModelInfo ocrDetModel;
ocrDetModel.modelId = 2;
ocrDetModel.algoType = OCR_DET;
ocrDetModel.modelPath = "ocr_det.trtmodel";

// OCR 识别模型
stAIModelInfo ocrRecModel;
ocrRecModel.modelId = 3;
ocrRecModel.algoType = OCR_REC;
ocrRecModel.modelPath = "ocr_rec.trtmodel";
ocrRecModel.modleLabelPath = "ppocr_keys_v1.txt";

runtime->CreateModle(ocrDetModel);
runtime->CreateModle(ocrRecModel);

// 先进行文字检测，再进行文字识别
// 检测结果中包含文字区域的四个角点坐标
// 识别结果中 item.ocr_str 包含识别的文字内容
```

#### 图像分割
```cpp
stAIModelInfo segModel;
segModel.modelId = 4;
segModel.algoType = YOLOV8_SEG;
segModel.modelPath = "yolo8_seg.trtmodel";
segModel.inferParam.segThreshold = 0.5f;

runtime->CreateModle(segModel);

// 分割结果中 item.mask 包含分割掩码的轮廓点
```

## 开发指南

### 添加新算法

#### 1. 定义算法类型
在 [`AIRuntimeDataStruct.h`](include/public/AIRuntimeDataStruct.h:41) 中添加新的算法类型：

```cpp
enum eAIAlgoType : int {
    // ... 现有类型
    YOUR_NEW_ALGO,  // 添加新算法类型
};
```

#### 2. 实现算法类
在相应的后端目录下创建算法实现：

```cpp
// src/trt/trt_app_your_algo/your_algo.cpp
namespace YourAlgo {
    std::shared_ptr<Algo::Infer> create_infer(
        const std::string& engine_file,
        int gpu_id,
        float confidence_threshold
    ) {
        // 实现算法创建逻辑
    }
}
```

#### 3. 注册算法
在 [`AIRuntime.cpp`](src/airuntime/AIRuntime.cpp:506) 的 `create_trt_model` 函数中添加新算法的创建逻辑：

```cpp
case YOUR_NEW_ALGO:
    infer = YourAlgo::create_infer(modelInfo.modelPath, 
                                   modelInfo.inferParam.gpuId, 
                                   modelInfo.inferParam.confidenceThreshold);
    break;
```

#### 4. 处理结果转换
在 [`AIRuntime.cpp`](src/airuntime/AIRuntime.cpp:174) 的 `ToRuntimeResult` 函数中添加结果转换逻辑：

```cpp
case YOUR_NEW_ALGO:
    rst.code = box.class_label;
    rst.confidence = box.confidence;
    // 添加特定的结果处理逻辑
    break;
```

### 性能优化建议

#### 1. 内存管理
- 使用 `usePinMemory = true` 启用固定内存，提高 GPU 传输效率
- 合理设置 `GPUCachSize` 和 `CPUCachSize` 缓存大小
- 避免频繁的内存分配和释放

#### 2. 线程配置
- 根据硬件配置调整 `preProcessThreadCnt` 和 `inferThreadCnt`
- CPU 密集型任务增加预处理线程数
- GPU 推理任务通常 1-2 个推理线程即可

#### 3. 批处理优化
- 对于支持批处理的模型，设置合适的 `maxBatchSize`
- 使用动态批处理提高吞吐量
- 注意批处理对延迟的影响

#### 4. 模型优化
- 使用 TensorRT 进行模型优化和加速
- 选择合适的精度模式（FP32/FP16/INT8）
- 针对特定硬件进行模型优化

### 调试技巧

#### 1. 日志配置
项目使用 spdlog 进行日志记录，可以通过以下方式调整日志级别：

```cpp
// 在代码中设置日志级别
LOG_INFO("信息日志");
LOG_INFOW("警告日志");
LOG_INFOE("错误日志");
LOG_INFOD("调试日志");
```

#### 2. 性能分析
使用内置的时间统计功能：

```cpp
// 任务信息中包含详细的时间统计
std::cout << "预处理时间: " << result->taskInfo->preCostTime << "ms" << std::endl;
std::cout << "推理时间: " << result->taskInfo->inferCostTime << "ms" << std::endl;
std::cout << "后处理时间: " << result->taskInfo->hostCostTime << "ms" << std::endl;
std::cout << "总时间: " << result->taskInfo->totalCostTime << "ms" << std::endl;
```

#### 3. 内存泄漏检测
- 使用 Visual Studio 的诊断工具（Windows）
- 使用 Valgrind（Linux）
- 注意智能指针的正确使用

### 常见问题解决

#### 1. 模型加载失败
- 检查模型文件路径是否正确
- 确认模型格式与后端匹配（.trtmodel 用于 TensorRT，.onnx 用于 ONNX Runtime）
- 检查 GPU 内存是否足够

#### 2. 推理结果异常
- 检查输入图像的预处理是否正确
- 确认模型的输入尺寸和格式
- 验证后处理逻辑是否正确

#### 3. 性能问题
- 检查是否正确使用了 GPU 加速
- 优化批处理大小
- 检查内存拷贝是否过多

#### 4. 编译错误
- 确认 CUDA 版本与 TensorRT 版本兼容
- 检查第三方库路径配置
- 确认 C++17 标准支持

## 最佳实践

### 1. 错误处理
```cpp
eAIErrorCode result = runtime->CreateModle(modelInfo);
if (result != E_OK) {
    switch (result) {
        case E_FILE_NOT_EXIST:
            std::cerr << "模型文件不存在" << std::endl;
            break;
        case E_CREATE_MODEL_FAILED:
            std::cerr << "模型创建失败" << std::endl;
            break;
        case E_OUT_OF_MEMORY:
            std::cerr << "内存不足" << std::endl;
            break;
        default:
            std::cerr << "未知错误: " << result << std::endl;
    }
    return -1;
}
```

### 2. 资源管理
```cpp
class AIRuntimeWrapper {
private:
    AIRuntimeInterface* runtime_;
    
public:
    AIRuntimeWrapper() : runtime_(GetAIRuntime()) {
        stAIConfigInfo config;
        runtime_->InitRuntime(config);
    }
    
    ~AIRuntimeWrapper() {
        if (runtime_) {
            runtime_->DestoryRuntime();
        }
    }
    
    // 禁用拷贝构造和赋值
    AIRuntimeWrapper(const AIRuntimeWrapper&) = delete;
    AIRuntimeWrapper& operator=(const AIRuntimeWrapper&) = delete;
};
```

### 3. 配置管理
```cpp
// 使用 JSON 配置文件
json config = read_json_from_file("config.json");
stAIConfigInfo aiConfig(config["runtime"]);
stAIModelInfo modelInfo(config["models"][0]);
```

### 4. 多模型管理
```cpp
class ModelManager {
private:
    AIRuntimeInterface* runtime_;
    std::map<int, stAIModelInfo> models_;
    
public:
    bool LoadModelsFromConfig(const std::string& configFile) {
        json config = read_json_from_file(configFile.c_str());
        
        for (const auto& modelConfig : config["models"]) {
            stAIModelInfo modelInfo(modelConfig);
            if (runtime_->CreateModle(modelInfo) == E_OK) {
                models_[modelInfo.modelId] = modelInfo;
            } else {
                return false;
            }
        }
        return true;
    }
    
    ModelResultPtr Infer(int modelId, const cv::Mat& image) {
        if (models_.find(modelId) == models_.end()) {
            return nullptr;
        }
        
        TaskInfoPtr task = std::make_shared<stTaskInfo>();
        task->modelId = modelId;
        task->imageData = {image};
        
        return runtime_->RunInferTask(task);
    }
};
```

## 贡献指南

### 代码规范

#### 1. 命名规范
- **类名**: 使用 PascalCase，如 `AIRuntime`
- **函数名**: 使用 PascalCase，如 `CreateModle`
- **变量名**: 使用 camelCase，如 `modelInfo`
- **常量名**: 使用 UPPER_CASE，如 `E_OK`
- **文件名**: 使用 PascalCase，如 `AIRuntime.cpp`

#### 2. 代码格式
- 使用 4 个空格缩进
- 大括号另起一行
- 每行代码长度不超过 120 字符
- 在函数和类之间添加空行

#### 3. 注释规范
```cpp
/**
 * @brief 创建推理模型
 * @param modelInfo 模型配置信息
 * @return 错误码，E_OK 表示成功
 */
virtual eAIErrorCode CreateModle(stAIModelInfo& modelInfo) = 0;
```

### 提交流程

1. **Fork 项目**: 从主仓库 fork 一个副本
2. **创建分支**: 为新功能或修复创建专门的分支
3. **编写代码**: 遵循代码规范进行开发
4. **测试验证**: 确保新代码通过所有测试
5. **提交 PR**: 创建 Pull Request 并详细描述修改内容

### 测试要求

#### 1. 单元测试
- 为新增功能编写单元测试
- 确保测试覆盖率达到 80% 以上
- 使用 doctest 框架编写测试

#### 2. 集成测试
- 测试不同算法的端到端流程
- 验证多线程环境下的稳定性
- 测试异常情况的处理

#### 3. 性能测试
- 对比优化前后的性能指标
- 测试内存使用情况
- 验证 GPU 利用率

## 参考资料

### 官方文档
- [TensorRT Developer Guide](https://docs.nvidia.com/deeplearning/tensorrt/developer-guide/index.html)
- [ONNX Runtime Documentation](https://onnxruntime.ai/docs/)
- [OpenCV Documentation](https://docs.opencv.org/)

### 相关技术
- [CUDA Programming Guide](https://docs.nvidia.com/cuda/cuda-c-programming-guide/)
- [cuDNN Developer Guide](https://docs.nvidia.com/deeplearning/cudnn/developer-guide/index.html)
- [XMake Documentation](https://xmake.io/#/guide/introduction)

### 社区资源
- [NVIDIA Developer Forums](https://forums.developer.nvidia.com/)
- [OpenCV Community](https://opencv.org/community/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/tensorrt)

---

## 联系方式

如有问题或建议，请通过以下方式联系：

- **项目仓库**: 提交 Issue 或 Pull Request
- **技术讨论**: 参与项目讨论区
- **邮件联系**: 发送邮件至项目维护者

欢迎加入 AIRuntime 项目的开发和维护工作！